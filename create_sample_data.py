#!/usr/bin/env python
import os
import sys
import django

# إعداد Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'wallpaper_site.settings')
django.setup()

from accounts.models import User
from wallpapers.models import Category
from django.utils.text import slugify

def create_sample_data():
    print("إنشاء البيانات التجريبية...")
    
    # إنشاء مستخدم إداري إذا لم يكن موجوداً
    if not User.objects.filter(username='admin').exists():
        admin_user = User.objects.create_superuser(
            username='admin',
            email='<EMAIL>',
            password='admin123456',
            first_name='أحمد',
            last_name='المدير'
        )
        print("تم إنشاء المستخدم الإداري")
    
    # إنشاء فئات
    categories_data = [
        {'name': 'خلفيات كمبيوتر', 'icon': 'fas fa-desktop'},
        {'name': 'خلفيات هواتف', 'icon': 'fas fa-mobile-alt'},
        {'name': 'طبيعة', 'icon': 'fas fa-tree'},
        {'name': 'مدن', 'icon': 'fas fa-city'},
        {'name': 'فضاء', 'icon': 'fas fa-rocket'},
        {'name': 'سيارات', 'icon': 'fas fa-car'},
    ]
    
    for cat_data in categories_data:
        category, created = Category.objects.get_or_create(
            name=cat_data['name'],
            defaults={
                'slug': slugify(cat_data['name']),
                'icon': cat_data['icon'],
                'description': f'مجموعة رائعة من {cat_data["name"]}'
            }
        )
        if created:
            print(f"تم إنشاء فئة: {category.name}")
    
    print("تم إنشاء البيانات التجريبية بنجاح!")

if __name__ == '__main__':
    create_sample_data()
