from django.db import models
from django.contrib.auth import get_user_model
from django.urls import reverse
from PIL import Image
import os

User = get_user_model()


class Category(models.Model):
    """نموذج فئات الخلفيات"""
    name = models.Char<PERSON>ield(max_length=100, unique=True, verbose_name="اسم الفئة")
    slug = models.SlugField(max_length=100, unique=True, verbose_name="الرابط")
    description = models.TextField(blank=True, verbose_name="الوصف")
    icon = models.CharField(max_length=50, blank=True, verbose_name="أيقونة")
    is_active = models.BooleanField(default=True, verbose_name="نشط")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")

    class Meta:
        verbose_name = "فئة"
        verbose_name_plural = "الفئات"
        ordering = ['name']

    def __str__(self):
        return self.name

    def get_absolute_url(self):
        return reverse('wallpapers:category', kwargs={'slug': self.slug})


class Wallpaper(models.Model):
    """نموذج الخلفيات"""
    DEVICE_CHOICES = [
        ('desktop', 'كمبيوتر'),
        ('mobile', 'هاتف'),
        ('tablet', 'تابلت'),
        ('all', 'جميع الأجهزة'),
    ]

    RESOLUTION_CHOICES = [
        ('1920x1080', '1920x1080 (Full HD)'),
        ('2560x1440', '2560x1440 (2K)'),
        ('3840x2160', '3840x2160 (4K)'),
        ('1080x1920', '1080x1920 (Mobile)'),
        ('1440x2560', '1440x2560 (Mobile 2K)'),
        ('other', 'أخرى'),
    ]

    title = models.CharField(max_length=200, verbose_name="العنوان")
    slug = models.SlugField(max_length=200, unique=True, verbose_name="الرابط")
    description = models.TextField(blank=True, verbose_name="الوصف")
    image = models.ImageField(upload_to='wallpapers/', verbose_name="الصورة")
    thumbnail = models.ImageField(upload_to='thumbnails/', blank=True, verbose_name="الصورة المصغرة")
    category = models.ForeignKey(Category, on_delete=models.CASCADE, verbose_name="الفئة")
    device_type = models.CharField(max_length=10, choices=DEVICE_CHOICES, default='all', verbose_name="نوع الجهاز")
    resolution = models.CharField(max_length=20, choices=RESOLUTION_CHOICES, verbose_name="الدقة")
    file_size = models.PositiveIntegerField(blank=True, null=True, verbose_name="حجم الملف (بايت)")
    tags = models.CharField(max_length=500, blank=True, verbose_name="الكلمات المفتاحية")
    uploader = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name="الرافع")
    is_featured = models.BooleanField(default=False, verbose_name="مميز")
    is_active = models.BooleanField(default=True, verbose_name="نشط")
    download_count = models.PositiveIntegerField(default=0, verbose_name="عدد التحميلات")
    view_count = models.PositiveIntegerField(default=0, verbose_name="عدد المشاهدات")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")

    class Meta:
        verbose_name = "خلفية"
        verbose_name_plural = "الخلفيات"
        ordering = ['-created_at']

    def __str__(self):
        return self.title

    def get_absolute_url(self):
        return reverse('wallpapers:detail', kwargs={'slug': self.slug})

    def save(self, *args, **kwargs):
        # حفظ حجم الملف
        if self.image:
            self.file_size = self.image.size

        super().save(*args, **kwargs)

        # إنشاء صورة مصغرة
        if self.image and not self.thumbnail:
            self.create_thumbnail()

    def create_thumbnail(self):
        """إنشاء صورة مصغرة"""
        if not self.image:
            return

        img = Image.open(self.image.path)
        img.thumbnail((400, 300))

        # حفظ الصورة المصغرة
        thumb_name = f"thumb_{os.path.basename(self.image.name)}"
        thumb_path = os.path.join(os.path.dirname(self.image.path), 'thumbnails', thumb_name)

        # إنشاء مجلد الصور المصغرة إذا لم يكن موجوداً
        os.makedirs(os.path.dirname(thumb_path), exist_ok=True)

        img.save(thumb_path)
        self.thumbnail.name = f"thumbnails/{thumb_name}"
        self.save(update_fields=['thumbnail'])


class Download(models.Model):
    """نموذج تتبع التحميلات"""
    wallpaper = models.ForeignKey(Wallpaper, on_delete=models.CASCADE, verbose_name="الخلفية")
    user = models.ForeignKey(User, on_delete=models.CASCADE, null=True, blank=True, verbose_name="المستخدم")
    ip_address = models.GenericIPAddressField(verbose_name="عنوان IP")
    user_agent = models.TextField(blank=True, verbose_name="معلومات المتصفح")
    downloaded_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ التحميل")

    class Meta:
        verbose_name = "تحميل"
        verbose_name_plural = "التحميلات"
        ordering = ['-downloaded_at']

    def __str__(self):
        return f"{self.wallpaper.title} - {self.downloaded_at}"


class Favorite(models.Model):
    """نموذج المفضلة"""
    user = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name="المستخدم")
    wallpaper = models.ForeignKey(Wallpaper, on_delete=models.CASCADE, verbose_name="الخلفية")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإضافة")

    class Meta:
        verbose_name = "مفضلة"
        verbose_name_plural = "المفضلات"
        unique_together = ['user', 'wallpaper']
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.user.get_full_name()} - {self.wallpaper.title}"
