from django import forms
from django.contrib.auth import get_user_model
from crispy_forms.helper import FormHelper
from crispy_forms.layout import Layout, Submit, Row, Column, Field, HTML
from crispy_forms.bootstrap import FormActions
from .models import Wallpaper, Category

User = get_user_model()


class WallpaperSearchForm(forms.Form):
    """نموذج البحث عن الخلفيات"""
    DEVICE_CHOICES = [
        ('', 'جميع الأجهزة'),
        ('desktop', 'كمبيوتر'),
        ('mobile', 'هاتف'),
        ('tablet', 'تابلت'),
        ('all', 'جميع الأجهزة'),
    ]
    
    SORT_CHOICES = [
        ('-created_at', 'الأحدث'),
        ('-download_count', 'الأكثر تحميلاً'),
        ('-view_count', 'الأكثر مشاهدة'),
        ('title', 'الاسم (أ-ي)'),
    ]
    
    q = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'ابحث عن خلفيات...',
            'autocomplete': 'off'
        }),
        label='البحث'
    )
    
    category = forms.ModelChoiceField(
        queryset=Category.objects.filter(is_active=True),
        required=False,
        empty_label='جميع الفئات',
        widget=forms.Select(attrs={'class': 'form-select'}),
        label='الفئة'
    )
    
    device_type = forms.ChoiceField(
        choices=DEVICE_CHOICES,
        required=False,
        widget=forms.Select(attrs={'class': 'form-select'}),
        label='نوع الجهاز'
    )
    
    sort = forms.ChoiceField(
        choices=SORT_CHOICES,
        required=False,
        initial='-created_at',
        widget=forms.Select(attrs={'class': 'form-select'}),
        label='ترتيب حسب'
    )
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.form_method = 'get'
        self.helper.form_class = 'search-form'
        
        self.helper.layout = Layout(
            Row(
                Column('q', css_class='col-md-4 mb-3'),
                Column('category', css_class='col-md-2 mb-3'),
                Column('device_type', css_class='col-md-2 mb-3'),
                Column('sort', css_class='col-md-2 mb-3'),
                Column(
                    Submit('submit', 'بحث', css_class='btn btn-primary'),
                    css_class='col-md-2 mb-3 d-flex align-items-end'
                ),
                css_class='form-row'
            )
        )


class WallpaperUploadForm(forms.ModelForm):
    """نموذج رفع الخلفيات"""
    
    class Meta:
        model = Wallpaper
        fields = ['title', 'description', 'image', 'category', 'device_type', 'resolution', 'tags']
        widgets = {
            'title': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'عنوان الخلفية'
            }),
            'description': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 4,
                'placeholder': 'وصف الخلفية (اختياري)'
            }),
            'image': forms.FileInput(attrs={
                'class': 'form-control',
                'accept': 'image/*'
            }),
            'category': forms.Select(attrs={'class': 'form-select'}),
            'device_type': forms.Select(attrs={'class': 'form-select'}),
            'resolution': forms.Select(attrs={'class': 'form-select'}),
            'tags': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'الكلمات المفتاحية (مفصولة بفواصل)'
            }),
        }
    
    def __init__(self, *args, **kwargs):
        self.user = kwargs.pop('user', None)
        super().__init__(*args, **kwargs)
        
        self.helper = FormHelper()
        self.helper.form_method = 'post'
        self.helper.form_enctype = 'multipart/form-data'
        
        self.helper.layout = Layout(
            Field('title', css_class='form-group mb-3'),
            Field('description', css_class='form-group mb-3'),
            Field('image', css_class='form-group mb-3'),
            Row(
                Column('category', css_class='form-group col-md-6 mb-3'),
                Column('device_type', css_class='form-group col-md-6 mb-3'),
                css_class='form-row'
            ),
            Field('resolution', css_class='form-group mb-3'),
            Field('tags', css_class='form-group mb-3'),
            HTML('<small class="form-text text-muted mb-3">الحد الأقصى لحجم الملف: 10 ميجابايت</small>'),
            FormActions(
                Submit('submit', 'رفع الخلفية', css_class='btn btn-primary btn-lg')
            )
        )
    
    def clean_image(self):
        image = self.cleaned_data.get('image')
        if image:
            # التحقق من حجم الملف (10 ميجابايت)
            if image.size > 10 * 1024 * 1024:
                raise forms.ValidationError('حجم الملف كبير جداً. الحد الأقصى 10 ميجابايت.')
            
            # التحقق من نوع الملف
            if not image.content_type.startswith('image/'):
                raise forms.ValidationError('يجب أن يكون الملف صورة.')
        
        return image
    
    def save(self, commit=True):
        wallpaper = super().save(commit=False)
        if self.user:
            wallpaper.uploader = self.user
        
        if commit:
            wallpaper.save()
        
        return wallpaper


class WallpaperFilterForm(forms.Form):
    """نموذج تصفية الخلفيات"""
    RESOLUTION_CHOICES = [
        ('', 'جميع الدقات'),
        ('1920x1080', '1920x1080 (Full HD)'),
        ('2560x1440', '2560x1440 (2K)'),
        ('3840x2160', '3840x2160 (4K)'),
        ('1080x1920', '1080x1920 (Mobile)'),
        ('1440x2560', '1440x2560 (Mobile 2K)'),
    ]
    
    min_downloads = forms.IntegerField(
        required=False,
        min_value=0,
        widget=forms.NumberInput(attrs={
            'class': 'form-control',
            'placeholder': 'الحد الأدنى للتحميلات'
        }),
        label='الحد الأدنى للتحميلات'
    )
    
    resolution = forms.ChoiceField(
        choices=RESOLUTION_CHOICES,
        required=False,
        widget=forms.Select(attrs={'class': 'form-select'}),
        label='الدقة'
    )
    
    is_featured = forms.BooleanField(
        required=False,
        widget=forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        label='مميزة فقط'
    )
