from django.contrib import admin
from .models import Notification, EmailNotification


@admin.register(Notification)
class NotificationAdmin(admin.ModelAdmin):
    list_display = ('title', 'recipient', 'notification_type', 'is_read', 'created_at')
    list_filter = ('notification_type', 'is_read', 'created_at')
    search_fields = ('title', 'message', 'recipient__email')
    readonly_fields = ('created_at', 'read_at')

    actions = ['mark_as_read', 'mark_as_unread']

    def mark_as_read(self, request, queryset):
        queryset.update(is_read=True)
        self.message_user(request, f"تم تحديد {queryset.count()} إشعار كمقروء")
    mark_as_read.short_description = "تحديد كمقروء"

    def mark_as_unread(self, request, queryset):
        queryset.update(is_read=False)
        self.message_user(request, f"تم تحديد {queryset.count()} إشعار كغير مقروء")
    mark_as_unread.short_description = "تحديد كغير مقروء"


@admin.register(EmailNotification)
class EmailNotificationAdmin(admin.ModelAdmin):
    list_display = ('subject', 'recipient_email', 'is_sent', 'sent_at', 'created_at')
    list_filter = ('is_sent', 'sent_at', 'created_at')
    search_fields = ('subject', 'recipient_email', 'message')
    readonly_fields = ('sent_at', 'created_at', 'error_message')
