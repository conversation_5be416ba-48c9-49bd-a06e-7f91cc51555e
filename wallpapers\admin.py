from django.contrib import admin
from .models import Category, Wallpaper, Download, Favorite


@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    list_display = ('name', 'slug', 'is_active', 'created_at')
    list_filter = ('is_active', 'created_at')
    search_fields = ('name', 'description')
    prepopulated_fields = {'slug': ('name',)}


@admin.register(Wallpaper)
class WallpaperAdmin(admin.ModelAdmin):
    list_display = ('title', 'category', 'device_type', 'resolution', 'uploader', 'is_featured', 'is_active', 'download_count', 'created_at')
    list_filter = ('category', 'device_type', 'resolution', 'is_featured', 'is_active', 'created_at')
    search_fields = ('title', 'description', 'tags')
    prepopulated_fields = {'slug': ('title',)}
    readonly_fields = ('download_count', 'view_count', 'file_size', 'created_at', 'updated_at')

    fieldsets = (
        ('معلومات أساسية', {
            'fields': ('title', 'slug', 'description', 'category', 'uploader')
        }),
        ('الصورة', {
            'fields': ('image', 'thumbnail')
        }),
        ('تفاصيل تقنية', {
            'fields': ('device_type', 'resolution', 'file_size', 'tags')
        }),
        ('إعدادات', {
            'fields': ('is_featured', 'is_active')
        }),
        ('إحصائيات', {
            'fields': ('download_count', 'view_count', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )


@admin.register(Download)
class DownloadAdmin(admin.ModelAdmin):
    list_display = ('wallpaper', 'user', 'ip_address', 'downloaded_at')
    list_filter = ('downloaded_at',)
    search_fields = ('wallpaper__title', 'user__email', 'ip_address')
    readonly_fields = ('wallpaper', 'user', 'ip_address', 'user_agent', 'downloaded_at')


@admin.register(Favorite)
class FavoriteAdmin(admin.ModelAdmin):
    list_display = ('user', 'wallpaper', 'created_at')
    list_filter = ('created_at',)
    search_fields = ('user__email', 'wallpaper__title')
