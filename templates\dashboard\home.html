{% extends 'dashboard/base.html' %}

{% block title %}الرئيسية - لوحة التحكم{% endblock %}
{% block page_title %}نظرة عامة{% endblock %}

{% block content %}
<!-- إحصائيات سريعة -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stat-card">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0">{{ total_users }}</h4>
                        <p class="mb-0">إجمالي المستخدمين</p>
                        <small class="text-light">+{{ new_users_this_month }} هذا الشهر</small>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-users fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stat-card success">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0">{{ total_wallpapers }}</h4>
                        <p class="mb-0">إجمالي الخلفيات</p>
                        <small class="text-light">+{{ new_wallpapers_this_month }} هذا الشهر</small>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-images fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stat-card info">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0">{{ total_downloads }}</h4>
                        <p class="mb-0">إجمالي التحميلات</p>
                        <small class="text-light">+{{ downloads_this_month }} هذا الشهر</small>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-download fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stat-card warning">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0">{{ total_categories }}</h4>
                        <p class="mb-0">الفئات</p>
                        <small class="text-light">نشطة</small>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-folder fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- الرسوم البيانية -->
<div class="row mb-4">
    <div class="col-lg-8 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">إحصائيات التحميلات الأسبوعية</h5>
            </div>
            <div class="card-body">
                <canvas id="downloadsChart" height="100"></canvas>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">الإجراءات السريعة</h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{% url 'dashboard:wallpapers' %}" class="btn btn-primary">
                        <i class="fas fa-images me-2"></i>إدارة الخلفيات
                    </a>
                    <a href="{% url 'dashboard:users' %}" class="btn btn-success">
                        <i class="fas fa-users me-2"></i>إدارة المستخدمين
                    </a>
                    <a href="{% url 'dashboard:statistics' %}" class="btn btn-info">
                        <i class="fas fa-chart-bar me-2"></i>عرض الإحصائيات
                    </a>
                    <a href="{% url 'dashboard:settings' %}" class="btn btn-warning">
                        <i class="fas fa-cog me-2"></i>إعدادات الموقع
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- الجداول -->
<div class="row">
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">أحدث المستخدمين</h5>
                <a href="{% url 'dashboard:users' %}" class="btn btn-sm btn-outline-primary">عرض الكل</a>
            </div>
            <div class="card-body">
                {% if recent_users %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>الاسم</th>
                                    <th>البريد الإلكتروني</th>
                                    <th>تاريخ التسجيل</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for user in recent_users %}
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                {% if user.avatar %}
                                                    <img src="{{ user.avatar.url }}" alt="{{ user.get_full_name }}" 
                                                         class="rounded-circle me-2" width="32" height="32">
                                                {% else %}
                                                    <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center me-2" 
                                                         style="width: 32px; height: 32px;">
                                                        <i class="fas fa-user text-white"></i>
                                                    </div>
                                                {% endif %}
                                                {{ user.get_full_name }}
                                            </div>
                                        </td>
                                        <td>{{ user.email }}</td>
                                        <td>{{ user.date_joined|date:"d/m/Y" }}</td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <p class="text-muted text-center py-3">لا توجد مستخدمين جدد</p>
                {% endif %}
            </div>
        </div>
    </div>
    
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">أحدث الخلفيات</h5>
                <a href="{% url 'dashboard:wallpapers' %}" class="btn btn-sm btn-outline-primary">عرض الكل</a>
            </div>
            <div class="card-body">
                {% if recent_wallpapers %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>العنوان</th>
                                    <th>الرافع</th>
                                    <th>التحميلات</th>
                                    <th>الحالة</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for wallpaper in recent_wallpapers %}
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                {% if wallpaper.thumbnail %}
                                                    <img src="{{ wallpaper.thumbnail.url }}" alt="{{ wallpaper.title }}" 
                                                         class="rounded me-2" width="40" height="30" style="object-fit: cover;">
                                                {% endif %}
                                                {{ wallpaper.title|truncatechars:30 }}
                                            </div>
                                        </td>
                                        <td>{{ wallpaper.uploader.get_full_name }}</td>
                                        <td>{{ wallpaper.download_count }}</td>
                                        <td>
                                            {% if wallpaper.is_active %}
                                                <span class="badge bg-success">نشط</span>
                                            {% else %}
                                                <span class="badge bg-danger">غير نشط</span>
                                            {% endif %}
                                            {% if wallpaper.is_featured %}
                                                <span class="badge bg-warning">مميز</span>
                                            {% endif %}
                                        </td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <p class="text-muted text-center py-3">لا توجد خلفيات جديدة</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- الخلفيات الأكثر شعبية -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">الخلفيات الأكثر تحميلاً</h5>
            </div>
            <div class="card-body">
                {% if popular_wallpapers %}
                    <div class="row">
                        {% for wallpaper in popular_wallpapers %}
                            <div class="col-lg-2 col-md-3 col-sm-4 col-6 mb-3">
                                <div class="card">
                                    {% if wallpaper.thumbnail %}
                                        <img src="{{ wallpaper.thumbnail.url }}" class="card-img-top" 
                                             alt="{{ wallpaper.title }}" style="height: 120px; object-fit: cover;">
                                    {% endif %}
                                    <div class="card-body p-2">
                                        <h6 class="card-title small mb-1">{{ wallpaper.title|truncatechars:20 }}</h6>
                                        <small class="text-muted">
                                            <i class="fas fa-download me-1"></i>{{ wallpaper.download_count }}
                                        </small>
                                    </div>
                                </div>
                            </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <p class="text-muted text-center py-3">لا توجد خلفيات</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // رسم بياني للتحميلات الأسبوعية
    const ctx = document.getElementById('downloadsChart').getContext('2d');
    const downloadsChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: [
                {% for stat in weekly_stats %}
                    '{{ stat.date }}'{% if not forloop.last %},{% endif %}
                {% endfor %}
            ],
            datasets: [{
                label: 'التحميلات',
                data: [
                    {% for stat in weekly_stats %}
                        {{ stat.downloads }}{% if not forloop.last %},{% endif %}
                    {% endfor %}
                ],
                borderColor: 'rgb(102, 126, 234)',
                backgroundColor: 'rgba(102, 126, 234, 0.1)',
                tension: 0.4,
                fill: true
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
</script>
{% endblock %}
