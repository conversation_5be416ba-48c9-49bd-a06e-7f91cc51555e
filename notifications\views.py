from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib.auth import get_user_model
from django.http import JsonResponse
from django.views.decorators.http import require_POST
from django.core.paginator import Paginator
from django.contrib import messages
from django.core.mail import send_mail
from django.conf import settings
from django.template.loader import render_to_string
from django.utils import timezone
from .models import Notification, EmailNotification

User = get_user_model()


@login_required
def notification_list(request):
    """قائمة الإشعارات للمستخدم"""
    notifications_list = Notification.objects.filter(
        recipient=request.user
    ).order_by('-created_at')

    # ترقيم الصفحات
    paginator = Paginator(notifications_list, 20)
    page_number = request.GET.get('page')
    notifications = paginator.get_page(page_number)

    # إحصائيات الإشعارات
    unread_count = Notification.objects.filter(
        recipient=request.user,
        is_read=False
    ).count()

    context = {
        'notifications': notifications,
        'unread_count': unread_count,
    }

    return render(request, 'notifications/list.html', context)


@login_required
@require_POST
def mark_as_read(request, pk):
    """تحديد الإشعار كمقروء"""
    notification = get_object_or_404(
        Notification,
        pk=pk,
        recipient=request.user
    )

    notification.mark_as_read()

    return JsonResponse({
        'success': True,
        'message': 'تم تحديد الإشعار كمقروء'
    })


@login_required
@require_POST
def mark_all_as_read(request):
    """تحديد جميع الإشعارات كمقروءة"""
    Notification.objects.filter(
        recipient=request.user,
        is_read=False
    ).update(is_read=True, read_at=timezone.now())

    return JsonResponse({
        'success': True,
        'message': 'تم تحديد جميع الإشعارات كمقروءة'
    })


@login_required
@require_POST
def delete_notification(request, pk):
    """حذف الإشعار"""
    notification = get_object_or_404(
        Notification,
        pk=pk,
        recipient=request.user
    )

    notification.delete()

    return JsonResponse({
        'success': True,
        'message': 'تم حذف الإشعار'
    })


@login_required
def get_unread_notifications(request):
    """الحصول على الإشعارات غير المقروءة (AJAX)"""
    notifications = Notification.objects.filter(
        recipient=request.user,
        is_read=False
    ).order_by('-created_at')[:10]

    notifications_data = []
    for notification in notifications:
        notifications_data.append({
            'id': notification.id,
            'title': notification.title,
            'message': notification.message,
            'type': notification.notification_type,
            'url': notification.url,
            'created_at': notification.created_at.strftime('%Y-%m-%d %H:%M'),
        })

    return JsonResponse({
        'notifications': notifications_data,
        'unread_count': len(notifications_data)
    })


def create_notification(recipient, title, message, notification_type='system', url=''):
    """إنشاء إشعار جديد"""
    notification = Notification.objects.create(
        recipient=recipient,
        title=title,
        message=message,
        notification_type=notification_type,
        url=url
    )
    return notification


def send_welcome_notification(user):
    """إرسال إشعار ترحيب للمستخدم الجديد"""
    create_notification(
        recipient=user,
        title='مرحباً بك في موقع الخلفيات!',
        message=f'أهلاً وسهلاً {user.get_full_name()}، نتمنى لك تجربة رائعة في موقعنا.',
        notification_type='welcome',
        url='/accounts/profile/'
    )


def send_new_wallpaper_notification(wallpaper):
    """إرسال إشعار عند إضافة خلفية جديدة"""
    # إشعار لجميع المستخدمين المهتمين بهذه الفئة
    users = User.objects.filter(is_active=True)

    for user in users:
        create_notification(
            recipient=user,
            title='خلفية جديدة متاحة!',
            message=f'تم إضافة خلفية جديدة "{wallpaper.title}" في فئة {wallpaper.category.name}',
            notification_type='new_wallpaper',
            url=f'/wallpaper/{wallpaper.slug}/'
        )


def send_email_notification(recipient_email, subject, message, template_name=None, context=None):
    """إرسال إشعار عبر البريد الإلكتروني"""
    try:
        if template_name and context:
            html_message = render_to_string(template_name, context)
            send_mail(
                subject=subject,
                message=message,
                from_email=settings.DEFAULT_FROM_EMAIL,
                recipient_list=[recipient_email],
                html_message=html_message,
                fail_silently=False,
            )
        else:
            send_mail(
                subject=subject,
                message=message,
                from_email=settings.DEFAULT_FROM_EMAIL,
                recipient_list=[recipient_email],
                fail_silently=False,
            )

        # تسجيل الإشعار في قاعدة البيانات
        EmailNotification.objects.create(
            recipient_email=recipient_email,
            subject=subject,
            message=message,
            is_sent=True,
            sent_at=timezone.now()
        )

        return True

    except Exception as e:
        # تسجيل الخطأ
        EmailNotification.objects.create(
            recipient_email=recipient_email,
            subject=subject,
            message=message,
            is_sent=False,
            error_message=str(e)
        )

        return False
