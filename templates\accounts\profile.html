{% extends 'base.html' %}
{% load crispy_forms_tags %}

{% block title %}الملف الشخصي - موقع الخلفيات{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row">
        <div class="col-md-4">
            <div class="card shadow mb-4">
                <div class="card-body text-center">
                    {% if user.avatar %}
                        <img src="{{ user.avatar.url }}" alt="الصورة الشخصية" class="rounded-circle mb-3" width="150" height="150" style="object-fit: cover;">
                    {% else %}
                        <div class="bg-primary rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 150px; height: 150px;">
                            <i class="fas fa-user fa-4x text-white"></i>
                        </div>
                    {% endif %}
                    <h4>{{ user.get_full_name }}</h4>
                    <p class="text-muted">{{ user.email }}</p>
                    {% if user.bio %}
                        <p class="small">{{ user.bio }}</p>
                    {% endif %}
                    <div class="row text-center mt-3">
                        <div class="col-4">
                            <strong>{{ user_wallpapers_count|default:0 }}</strong>
                            <small class="d-block text-muted">خلفيات</small>
                        </div>
                        <div class="col-4">
                            <strong>{{ user_favorites_count|default:0 }}</strong>
                            <small class="d-block text-muted">مفضلة</small>
                        </div>
                        <div class="col-4">
                            <strong>{{ user_downloads_count|default:0 }}</strong>
                            <small class="d-block text-muted">تحميل</small>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card shadow">
                <div class="card-body">
                    <h5 class="card-title">معلومات إضافية</h5>
                    <ul class="list-unstyled">
                        <li><i class="fas fa-calendar me-2"></i> انضم في {{ user.date_joined|date:"d/m/Y" }}</li>
                        {% if user.date_of_birth %}
                            <li><i class="fas fa-birthday-cake me-2"></i> {{ user.date_of_birth|date:"d/m/Y" }}</li>
                        {% endif %}
                        {% if user.phone %}
                            <li><i class="fas fa-phone me-2"></i> {{ user.phone }}</li>
                        {% endif %}
                        <li>
                            <i class="fas fa-check-circle me-2 {% if user.is_verified %}text-success{% else %}text-muted{% endif %}"></i>
                            {% if user.is_verified %}حساب موثق{% else %}حساب غير موثق{% endif %}
                        </li>
                    </ul>
                </div>
            </div>
        </div>
        
        <div class="col-md-8">
            <div class="card shadow">
                <div class="card-header">
                    <h5 class="mb-0">تحديث الملف الشخصي</h5>
                </div>
                <div class="card-body">
                    {% crispy form %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    .card {
        border: none;
        border-radius: 1rem;
    }
    
    .card-header {
        background: linear-gradient(45deg, #667eea, #764ba2);
        color: white;
        border-radius: 1rem 1rem 0 0 !important;
        border: none;
    }
    
    .form-control:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    }
    
    .btn-primary {
        background: linear-gradient(45deg, #667eea, #764ba2);
        border: none;
        border-radius: 0.5rem;
        padding: 0.75rem 1.5rem;
        font-weight: 600;
        transition: all 0.3s ease;
    }
    
    .btn-primary:hover {
        background: linear-gradient(45deg, #5a6fd8, #6a4190);
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
    }
    
    .list-unstyled li {
        padding: 0.5rem 0;
        border-bottom: 1px solid #eee;
    }
    
    .list-unstyled li:last-child {
        border-bottom: none;
    }
</style>
{% endblock %}
