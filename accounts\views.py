from django.shortcuts import render, redirect
from django.contrib.auth import login, authenticate
from django.contrib.auth.decorators import login_required
from django.contrib.auth.views import LoginView, LogoutView, PasswordResetView
from django.contrib import messages
from django.urls import reverse_lazy
from django.views.generic import CreateView, UpdateView
from django.contrib.auth.mixins import LoginRequiredMixin
from .forms import CustomUserCreationForm, CustomAuthenticationForm, CustomPasswordResetForm, UserProfileForm
from .models import User


class CustomLoginView(LoginView):
    """عرض تسجيل الدخول"""
    form_class = CustomAuthenticationForm
    template_name = 'accounts/login.html'
    redirect_authenticated_user = True

    def get_success_url(self):
        return reverse_lazy('wallpapers:home')

    def form_valid(self, form):
        messages.success(self.request, f'مرحباً بك {form.get_user().get_full_name()}!')
        return super().form_valid(form)


class CustomLogoutView(LogoutView):
    """عرض تسجيل الخروج"""
    next_page = reverse_lazy('wallpapers:home')

    def dispatch(self, request, *args, **kwargs):
        messages.info(request, 'تم تسجيل الخروج بنجاح')
        return super().dispatch(request, *args, **kwargs)


class SignUpView(CreateView):
    """عرض إنشاء حساب جديد"""
    model = User
    form_class = CustomUserCreationForm
    template_name = 'accounts/signup.html'
    success_url = reverse_lazy('accounts:login')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'تم إنشاء حسابك بنجاح! يمكنك الآن تسجيل الدخول.')
        return response

    def dispatch(self, request, *args, **kwargs):
        if request.user.is_authenticated:
            return redirect('wallpapers:home')
        return super().dispatch(request, *args, **kwargs)


class CustomPasswordResetView(PasswordResetView):
    """عرض استعادة كلمة المرور"""
    form_class = CustomPasswordResetForm
    template_name = 'accounts/password_reset.html'
    email_template_name = 'accounts/password_reset_email.html'
    success_url = reverse_lazy('accounts:password_reset_done')

    def form_valid(self, form):
        messages.success(self.request, 'تم إرسال رابط استعادة كلمة المرور إلى بريدك الإلكتروني.')
        return super().form_valid(form)


class ProfileView(LoginRequiredMixin, UpdateView):
    """عرض الملف الشخصي"""
    model = User
    form_class = UserProfileForm
    template_name = 'accounts/profile.html'
    success_url = reverse_lazy('accounts:profile')

    def get_object(self):
        return self.request.user

    def form_valid(self, form):
        messages.success(self.request, 'تم تحديث ملفك الشخصي بنجاح!')
        return super().form_valid(form)


@login_required
def dashboard_view(request):
    """لوحة تحكم المستخدم"""
    from wallpapers.models import Wallpaper, Favorite, Download

    # إحصائيات المستخدم
    user_wallpapers = Wallpaper.objects.filter(uploader=request.user)
    user_favorites = Favorite.objects.filter(user=request.user)
    user_downloads = Download.objects.filter(user=request.user)

    context = {
        'user_wallpapers_count': user_wallpapers.count(),
        'user_favorites_count': user_favorites.count(),
        'user_downloads_count': user_downloads.count(),
        'recent_wallpapers': user_wallpapers[:5],
        'recent_favorites': user_favorites[:5],
        'recent_downloads': user_downloads[:5],
    }

    return render(request, 'accounts/dashboard.html', context)
