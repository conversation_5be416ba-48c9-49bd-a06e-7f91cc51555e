{% extends 'base.html' %}

{% block title %}{{ wallpaper.title }} - موقع الخلفيات{% endblock %}

{% block content %}
<div class="container py-4">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'wallpapers:home' %}">الرئيسية</a></li>
            <li class="breadcrumb-item"><a href="{% url 'wallpapers:category' wallpaper.category.slug %}">{{ wallpaper.category.name }}</a></li>
            <li class="breadcrumb-item active">{{ wallpaper.title }}</li>
        </ol>
    </nav>
    
    <div class="row">
        <!-- Wallpaper Image -->
        <div class="col-lg-8 mb-4">
            <div class="card">
                <div class="position-relative">
                    <img src="{{ wallpaper.image.url }}" 
                         class="card-img-top" 
                         alt="{{ wallpaper.title }}" 
                         style="max-height: 600px; object-fit: contain; width: 100%;">
                    
                    <!-- Badges -->
                    <div class="position-absolute top-0 start-0 m-3">
                        {% if wallpaper.is_featured %}
                            <span class="badge bg-warning mb-2">
                                <i class="fas fa-star me-1"></i>مميز
                            </span><br>
                        {% endif %}
                        <span class="badge bg-info">{{ wallpaper.get_device_type_display }}</span>
                    </div>
                    
                    <!-- Action Buttons -->
                    <div class="position-absolute top-0 end-0 m-3">
                        {% if user.is_authenticated %}
                            <button class="btn btn-light btn-sm rounded-circle me-2 favorite-btn" 
                                    data-slug="{{ wallpaper.slug }}"
                                    title="إضافة للمفضلة">
                                <i class="fas fa-heart {% if is_favorite %}text-danger{% endif %}"></i>
                            </button>
                        {% endif %}
                        <button class="btn btn-light btn-sm rounded-circle" 
                                onclick="shareWallpaper()"
                                title="مشاركة">
                            <i class="fas fa-share-alt"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Wallpaper Info -->
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h4 class="mb-0">{{ wallpaper.title }}</h4>
                </div>
                <div class="card-body">
                    {% if wallpaper.description %}
                        <p class="text-muted">{{ wallpaper.description }}</p>
                    {% endif %}
                    
                    <!-- Download Button -->
                    <div class="d-grid gap-2 mb-4">
                        <button class="btn btn-primary btn-lg download-btn" 
                                data-slug="{{ wallpaper.slug }}">
                            <i class="fas fa-download me-2"></i>
                            تحميل الخلفية
                        </button>
                    </div>
                    
                    <!-- Wallpaper Details -->
                    <div class="row text-center mb-4">
                        <div class="col-4">
                            <div class="border rounded p-3">
                                <i class="fas fa-download text-primary fa-2x mb-2"></i>
                                <h6>{{ wallpaper.download_count }}</h6>
                                <small class="text-muted">تحميل</small>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="border rounded p-3">
                                <i class="fas fa-eye text-success fa-2x mb-2"></i>
                                <h6>{{ wallpaper.view_count }}</h6>
                                <small class="text-muted">مشاهدة</small>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="border rounded p-3">
                                <i class="fas fa-heart text-danger fa-2x mb-2"></i>
                                <h6>0</h6>
                                <small class="text-muted">إعجاب</small>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Technical Details -->
                    <h6>التفاصيل التقنية</h6>
                    <ul class="list-unstyled">
                        <li><strong>الدقة:</strong> {{ wallpaper.resolution }}</li>
                        <li><strong>نوع الجهاز:</strong> {{ wallpaper.get_device_type_display }}</li>
                        <li><strong>الفئة:</strong> {{ wallpaper.category.name }}</li>
                        {% if wallpaper.file_size %}
                            <li><strong>حجم الملف:</strong> {{ wallpaper.file_size|filesizeformat }}</li>
                        {% endif %}
                        <li><strong>تاريخ الرفع:</strong> {{ wallpaper.created_at|date:"d/m/Y" }}</li>
                        <li><strong>الرافع:</strong> {{ wallpaper.uploader.get_full_name }}</li>
                    </ul>
                    
                    <!-- Tags -->
                    {% if wallpaper.tags %}
                        <h6>الكلمات المفتاحية</h6>
                        <div class="mb-3">
                            {% for tag in wallpaper.tags|split:"," %}
                                <span class="badge bg-secondary me-1 mb-1">{{ tag.strip }}</span>
                            {% endfor %}
                        </div>
                    {% endif %}
                    
                    <!-- Report Button -->
                    <div class="text-center">
                        <button class="btn btn-outline-danger btn-sm" onclick="reportWallpaper()">
                            <i class="fas fa-flag me-1"></i>
                            الإبلاغ عن مشكلة
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Related Wallpapers -->
    {% if related_wallpapers %}
        <div class="row mt-5">
            <div class="col-12">
                <h3 class="mb-4">خلفيات مشابهة</h3>
                <div class="row">
                    {% for related in related_wallpapers %}
                        <div class="col-lg-2 col-md-3 col-sm-4 col-6 mb-3">
                            <a href="{% url 'wallpapers:detail' related.slug %}" class="text-decoration-none">
                                <div class="card wallpaper-card">
                                    {% if related.thumbnail %}
                                        <img src="{{ related.thumbnail.url }}" 
                                             class="card-img-top" 
                                             alt="{{ related.title }}" 
                                             style="height: 150px; object-fit: cover;">
                                    {% else %}
                                        <img src="{{ related.image.url }}" 
                                             class="card-img-top" 
                                             alt="{{ related.title }}" 
                                             style="height: 150px; object-fit: cover;">
                                    {% endif %}
                                    <div class="card-body p-2">
                                        <h6 class="card-title small mb-1">{{ related.title|truncatechars:20 }}</h6>
                                        <small class="text-muted">{{ related.download_count }} تحميل</small>
                                    </div>
                                </div>
                            </a>
                        </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    {% endif %}
</div>

<!-- Download Modal -->
<div class="modal fade" id="downloadModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تحميل الخلفية</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body text-center">
                <i class="fas fa-download fa-3x text-primary mb-3"></i>
                <h5>جاري تحضير التحميل...</h5>
                <p class="text-muted">سيبدأ التحميل تلقائياً خلال ثوانٍ قليلة</p>
                <div class="progress">
                    <div class="progress-bar progress-bar-striped progress-bar-animated" 
                         style="width: 100%"></div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // تحميل الخلفية
    document.querySelector('.download-btn').addEventListener('click', function() {
        const slug = this.dataset.slug;
        const modal = new bootstrap.Modal(document.getElementById('downloadModal'));
        modal.show();
        
        fetch(`/download/${slug}/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                'Content-Type': 'application/json',
            },
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                setTimeout(() => {
                    modal.hide();
                    // إنشاء رابط تحميل
                    const link = document.createElement('a');
                    link.href = data.download_url;
                    link.download = data.filename;
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                    
                    // تحديث عداد التحميلات
                    location.reload();
                }, 2000);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            modal.hide();
        });
    });
    
    // المفضلة
    const favoriteBtn = document.querySelector('.favorite-btn');
    if (favoriteBtn) {
        favoriteBtn.addEventListener('click', function() {
            const slug = this.dataset.slug;
            fetch(`/favorite/${slug}/`, {
                method: 'POST',
                headers: {
                    'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                    'Content-Type': 'application/json',
                },
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const icon = this.querySelector('i');
                    icon.classList.toggle('text-danger', data.is_favorite);
                    
                    // إظهار رسالة
                    const toast = document.createElement('div');
                    toast.className = 'toast position-fixed top-0 end-0 m-3';
                    toast.innerHTML = `
                        <div class="toast-body">
                            ${data.message}
                        </div>
                    `;
                    document.body.appendChild(toast);
                    const bsToast = new bootstrap.Toast(toast);
                    bsToast.show();
                    
                    setTimeout(() => {
                        document.body.removeChild(toast);
                    }, 3000);
                }
            });
        });
    }
    
    // مشاركة الخلفية
    function shareWallpaper() {
        if (navigator.share) {
            navigator.share({
                title: '{{ wallpaper.title }}',
                text: 'شاهد هذه الخلفية الرائعة',
                url: window.location.href
            });
        } else {
            // نسخ الرابط
            navigator.clipboard.writeText(window.location.href).then(() => {
                alert('تم نسخ الرابط!');
            });
        }
    }
    
    // الإبلاغ عن مشكلة
    function reportWallpaper() {
        if (confirm('هل تريد الإبلاغ عن مشكلة في هذه الخلفية؟')) {
            alert('تم إرسال البلاغ. شكراً لك!');
        }
    }
</script>
{% endblock %}
