from django.urls import path
from . import views

app_name = 'wallpapers'

urlpatterns = [
    # الصفحة الرئيسية
    path('', views.home_view, name='home'),
    
    # قائمة الخلفيات
    path('wallpapers/', views.WallpaperListView.as_view(), name='list'),
    path('category/<slug:category_slug>/', views.WallpaperListView.as_view(), name='category'),
    
    # تفاصيل الخلفية
    path('wallpaper/<slug:slug>/', views.WallpaperDetailView.as_view(), name='detail'),
    
    # تحميل الخلفية
    path('download/<slug:slug>/', views.download_wallpaper, name='download'),
    
    # المفضلة
    path('favorite/<slug:slug>/', views.toggle_favorite, name='toggle_favorite'),
    
    # رفع خلفية جديدة
    # path('upload/', views.upload_wallpaper, name='upload'),
    
    # البحث
    # path('search/', views.search_wallpapers, name='search'),
]
