{% extends 'base.html' %}
{% load crispy_forms_tags %}

{% block title %}
    {% if current_category %}
        {{ current_category.name }} - موقع الخلفيات
    {% else %}
        جميع الخلفيات - موقع الخلفيات
    {% endif %}
{% endblock %}

{% block content %}
<div class="container py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            {% if current_category %}
                <h1>{{ current_category.name }}</h1>
                {% if current_category.description %}
                    <p class="text-muted">{{ current_category.description }}</p>
                {% endif %}
            {% else %}
                <h1>جميع الخلفيات</h1>
                <p class="text-muted">تصفح مجموعتنا الكاملة من الخلفيات عالية الجودة</p>
            {% endif %}
        </div>
    </div>
    
    <!-- Search and Filters -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    {% crispy search_form %}
                </div>
            </div>
        </div>
    </div>
    
    <!-- Categories Sidebar and Results -->
    <div class="row">
        <!-- Categories Sidebar -->
        <div class="col-lg-3 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">الفئات</h5>
                </div>
                <div class="list-group list-group-flush">
                    <a href="{% url 'wallpapers:list' %}" 
                       class="list-group-item list-group-item-action {% if not current_category %}active{% endif %}">
                        جميع الفئات
                    </a>
                    {% for category in categories %}
                        <a href="{% url 'wallpapers:category' category.slug %}" 
                           class="list-group-item list-group-item-action {% if current_category.slug == category.slug %}active{% endif %}">
                            {{ category.name }}
                            <span class="badge bg-secondary float-end">{{ category.wallpaper_count }}</span>
                        </a>
                    {% endfor %}
                </div>
            </div>
            
            <!-- Quick Filters -->
            <div class="card mt-3">
                <div class="card-header">
                    <h6 class="mb-0">تصفية سريعة</h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label class="form-label">نوع الجهاز</label>
                        <div class="btn-group-vertical d-grid gap-2">
                            <a href="?device_type=desktop" class="btn btn-outline-primary btn-sm">كمبيوتر</a>
                            <a href="?device_type=mobile" class="btn btn-outline-primary btn-sm">هاتف</a>
                            <a href="?device_type=tablet" class="btn btn-outline-primary btn-sm">تابلت</a>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">ترتيب حسب</label>
                        <div class="btn-group-vertical d-grid gap-2">
                            <a href="?sort=-created_at" class="btn btn-outline-secondary btn-sm">الأحدث</a>
                            <a href="?sort=-download_count" class="btn btn-outline-secondary btn-sm">الأكثر تحميلاً</a>
                            <a href="?sort=-view_count" class="btn btn-outline-secondary btn-sm">الأكثر مشاهدة</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Wallpapers Grid -->
        <div class="col-lg-9">
            <!-- Results Info -->
            <div class="d-flex justify-content-between align-items-center mb-3">
                <p class="text-muted mb-0">
                    عرض {{ wallpapers|length }} من أصل {{ paginator.count }} خلفية
                </p>
                <div class="btn-group" role="group">
                    <button type="button" class="btn btn-outline-secondary btn-sm active" id="grid-view">
                        <i class="fas fa-th"></i>
                    </button>
                    <button type="button" class="btn btn-outline-secondary btn-sm" id="list-view">
                        <i class="fas fa-list"></i>
                    </button>
                </div>
            </div>
            
            <!-- Wallpapers -->
            {% if wallpapers %}
                <div class="row" id="wallpapers-container">
                    {% for wallpaper in wallpapers %}
                        <div class="col-lg-4 col-md-6 mb-4 wallpaper-item">
                            <div class="card wallpaper-card h-100">
                                <div class="position-relative">
                                    {% if wallpaper.thumbnail %}
                                        <img src="{{ wallpaper.thumbnail.url }}" 
                                             class="card-img-top" 
                                             alt="{{ wallpaper.title }}" 
                                             style="height: 250px; object-fit: cover;">
                                    {% else %}
                                        <img src="{{ wallpaper.image.url }}" 
                                             class="card-img-top" 
                                             alt="{{ wallpaper.title }}" 
                                             style="height: 250px; object-fit: cover;">
                                    {% endif %}
                                    
                                    <!-- Badges -->
                                    <div class="position-absolute top-0 start-0 m-2">
                                        {% if wallpaper.is_featured %}
                                            <span class="badge bg-warning mb-1">
                                                <i class="fas fa-star me-1"></i>مميز
                                            </span>
                                        {% endif %}
                                        <span class="badge bg-info">{{ wallpaper.device_type }}</span>
                                    </div>
                                    
                                    <!-- Favorite Button -->
                                    {% if user.is_authenticated %}
                                        <div class="position-absolute top-0 end-0 m-2">
                                            <button class="btn btn-light btn-sm rounded-circle favorite-btn" 
                                                    data-slug="{{ wallpaper.slug }}">
                                                <i class="fas fa-heart"></i>
                                            </button>
                                        </div>
                                    {% endif %}
                                </div>
                                
                                <div class="card-body d-flex flex-column">
                                    <h6 class="card-title">{{ wallpaper.title }}</h6>
                                    {% if wallpaper.description %}
                                        <p class="card-text text-muted small">
                                            {{ wallpaper.description|truncatewords:10 }}
                                        </p>
                                    {% endif %}
                                    
                                    <div class="row text-center mb-3 mt-auto">
                                        <div class="col-4">
                                            <small class="text-muted">
                                                <i class="fas fa-download"></i><br>
                                                {{ wallpaper.download_count }}
                                            </small>
                                        </div>
                                        <div class="col-4">
                                            <small class="text-muted">
                                                <i class="fas fa-eye"></i><br>
                                                {{ wallpaper.view_count }}
                                            </small>
                                        </div>
                                        <div class="col-4">
                                            <small class="text-muted">
                                                <i class="fas fa-expand-arrows-alt"></i><br>
                                                {{ wallpaper.resolution }}
                                            </small>
                                        </div>
                                    </div>
                                    
                                    <div class="d-grid gap-2">
                                        <a href="{% url 'wallpapers:detail' wallpaper.slug %}" 
                                           class="btn btn-primary btn-sm">
                                            عرض التفاصيل
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    {% endfor %}
                </div>
                
                <!-- Pagination -->
                {% if is_paginated %}
                    <nav aria-label="تصفح الصفحات">
                        <ul class="pagination justify-content-center">
                            {% if page_obj.has_previous %}
                                <li class="page-item">
                                    <a class="page-link" href="?page=1">الأولى</a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.previous_page_number }}">السابقة</a>
                                </li>
                            {% endif %}
                            
                            {% for num in page_obj.paginator.page_range %}
                                {% if page_obj.number == num %}
                                    <li class="page-item active">
                                        <span class="page-link">{{ num }}</span>
                                    </li>
                                {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ num }}">{{ num }}</a>
                                    </li>
                                {% endif %}
                            {% endfor %}
                            
                            {% if page_obj.has_next %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.next_page_number }}">التالية</a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}">الأخيرة</a>
                                </li>
                            {% endif %}
                        </ul>
                    </nav>
                {% endif %}
            {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-search fa-5x text-muted mb-3"></i>
                    <h4>لا توجد خلفيات</h4>
                    <p class="text-muted">لم نجد أي خلفيات تطابق معايير البحث الخاصة بك.</p>
                    <a href="{% url 'wallpapers:list' %}" class="btn btn-primary">عرض جميع الخلفيات</a>
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // تبديل عرض الشبكة/القائمة
    document.getElementById('list-view').addEventListener('click', function() {
        document.getElementById('wallpapers-container').className = 'row';
        document.querySelectorAll('.wallpaper-item').forEach(item => {
            item.className = 'col-12 mb-3 wallpaper-item';
        });
        this.classList.add('active');
        document.getElementById('grid-view').classList.remove('active');
    });
    
    document.getElementById('grid-view').addEventListener('click', function() {
        document.getElementById('wallpapers-container').className = 'row';
        document.querySelectorAll('.wallpaper-item').forEach(item => {
            item.className = 'col-lg-4 col-md-6 mb-4 wallpaper-item';
        });
        this.classList.add('active');
        document.getElementById('list-view').classList.remove('active');
    });
    
    // المفضلة
    document.querySelectorAll('.favorite-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const slug = this.dataset.slug;
            fetch(`/favorite/${slug}/`, {
                method: 'POST',
                headers: {
                    'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                    'Content-Type': 'application/json',
                },
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    this.classList.toggle('btn-danger', data.is_favorite);
                    this.classList.toggle('btn-light', !data.is_favorite);
                }
            });
        });
    });
</script>
{% endblock %}
