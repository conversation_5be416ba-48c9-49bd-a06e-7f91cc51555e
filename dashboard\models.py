from django.db import models
from django.contrib.auth import get_user_model

User = get_user_model()


class SiteSettings(models.Model):
    """إعدادات الموقع"""
    site_name = models.CharField(max_length=100, default="موقع الخلفيات", verbose_name="اسم الموقع")
    site_description = models.TextField(default="موقع لتحميل خلفيات عالية الجودة", verbose_name="وصف الموقع")
    site_logo = models.ImageField(upload_to='site/', blank=True, verbose_name="شعار الموقع")
    contact_email = models.EmailField(default="<EMAIL>", verbose_name="بريد التواصل")
    facebook_url = models.URLField(blank=True, verbose_name="رابط فيسبوك")
    twitter_url = models.URLField(blank=True, verbose_name="رابط تويتر")
    instagram_url = models.URLField(blank=True, verbose_name="رابط إنستغرام")
    youtube_url = models.URLField(blank=True, verbose_name="رابط يوتيوب")
    max_upload_size = models.PositiveIntegerField(default=10, verbose_name="الحد الأقصى لحجم الرفع (MB)")
    wallpapers_per_page = models.PositiveIntegerField(default=20, verbose_name="عدد الخلفيات في الصفحة")
    enable_user_uploads = models.BooleanField(default=True, verbose_name="السماح برفع المستخدمين")
    enable_comments = models.BooleanField(default=True, verbose_name="السماح بالتعليقات")
    maintenance_mode = models.BooleanField(default=False, verbose_name="وضع الصيانة")
    maintenance_message = models.TextField(blank=True, verbose_name="رسالة الصيانة")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")

    class Meta:
        verbose_name = "إعدادات الموقع"
        verbose_name_plural = "إعدادات الموقع"

    def __str__(self):
        return self.site_name

    def save(self, *args, **kwargs):
        # التأكد من وجود سجل واحد فقط
        if not self.pk and SiteSettings.objects.exists():
            raise ValueError("يمكن وجود سجل واحد فقط من إعدادات الموقع")
        super().save(*args, **kwargs)


class AdminLog(models.Model):
    """سجل أنشطة الإدارة"""
    ACTION_CHOICES = [
        ('create', 'إنشاء'),
        ('update', 'تحديث'),
        ('delete', 'حذف'),
        ('login', 'تسجيل دخول'),
        ('logout', 'تسجيل خروج'),
        ('upload', 'رفع'),
        ('download', 'تحميل'),
    ]

    admin_user = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name="المدير")
    action = models.CharField(max_length=20, choices=ACTION_CHOICES, verbose_name="الإجراء")
    target_model = models.CharField(max_length=100, verbose_name="النموذج المستهدف")
    target_id = models.PositiveIntegerField(null=True, blank=True, verbose_name="معرف الهدف")
    description = models.TextField(verbose_name="الوصف")
    ip_address = models.GenericIPAddressField(verbose_name="عنوان IP")
    user_agent = models.TextField(blank=True, verbose_name="معلومات المتصفح")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")

    class Meta:
        verbose_name = "سجل إداري"
        verbose_name_plural = "السجلات الإدارية"
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.admin_user.get_full_name()} - {self.action} - {self.created_at}"


class Statistics(models.Model):
    """إحصائيات الموقع"""
    date = models.DateField(unique=True, verbose_name="التاريخ")
    total_users = models.PositiveIntegerField(default=0, verbose_name="إجمالي المستخدمين")
    new_users = models.PositiveIntegerField(default=0, verbose_name="المستخدمون الجدد")
    total_wallpapers = models.PositiveIntegerField(default=0, verbose_name="إجمالي الخلفيات")
    new_wallpapers = models.PositiveIntegerField(default=0, verbose_name="الخلفيات الجديدة")
    total_downloads = models.PositiveIntegerField(default=0, verbose_name="إجمالي التحميلات")
    daily_downloads = models.PositiveIntegerField(default=0, verbose_name="التحميلات اليومية")
    page_views = models.PositiveIntegerField(default=0, verbose_name="مشاهدات الصفحة")
    unique_visitors = models.PositiveIntegerField(default=0, verbose_name="الزوار الفريدون")

    class Meta:
        verbose_name = "إحصائية"
        verbose_name_plural = "الإحصائيات"
        ordering = ['-date']

    def __str__(self):
        return f"إحصائيات {self.date}"
