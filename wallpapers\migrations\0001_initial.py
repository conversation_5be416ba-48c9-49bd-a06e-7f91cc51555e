# Generated by Django 5.2.3 on 2025-09-04 19:57

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Category',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True, verbose_name='اسم الفئة')),
                ('slug', models.SlugField(max_length=100, unique=True, verbose_name='الرابط')),
                ('description', models.TextField(blank=True, verbose_name='الوصف')),
                ('icon', models.CharField(blank=True, max_length=50, verbose_name='أيقونة')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
            ],
            options={
                'verbose_name': 'فئة',
                'verbose_name_plural': 'الفئات',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='Wallpaper',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200, verbose_name='العنوان')),
                ('slug', models.SlugField(max_length=200, unique=True, verbose_name='الرابط')),
                ('description', models.TextField(blank=True, verbose_name='الوصف')),
                ('image', models.ImageField(upload_to='wallpapers/', verbose_name='الصورة')),
                ('thumbnail', models.ImageField(blank=True, upload_to='thumbnails/', verbose_name='الصورة المصغرة')),
                ('device_type', models.CharField(choices=[('desktop', 'كمبيوتر'), ('mobile', 'هاتف'), ('tablet', 'تابلت'), ('all', 'جميع الأجهزة')], default='all', max_length=10, verbose_name='نوع الجهاز')),
                ('resolution', models.CharField(choices=[('1920x1080', '1920x1080 (Full HD)'), ('2560x1440', '2560x1440 (2K)'), ('3840x2160', '3840x2160 (4K)'), ('1080x1920', '1080x1920 (Mobile)'), ('1440x2560', '1440x2560 (Mobile 2K)'), ('other', 'أخرى')], max_length=20, verbose_name='الدقة')),
                ('file_size', models.PositiveIntegerField(blank=True, null=True, verbose_name='حجم الملف (بايت)')),
                ('tags', models.CharField(blank=True, max_length=500, verbose_name='الكلمات المفتاحية')),
                ('is_featured', models.BooleanField(default=False, verbose_name='مميز')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('download_count', models.PositiveIntegerField(default=0, verbose_name='عدد التحميلات')),
                ('view_count', models.PositiveIntegerField(default=0, verbose_name='عدد المشاهدات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('category', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='wallpapers.category', verbose_name='الفئة')),
                ('uploader', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='الرافع')),
            ],
            options={
                'verbose_name': 'خلفية',
                'verbose_name_plural': 'الخلفيات',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='Download',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('ip_address', models.GenericIPAddressField(verbose_name='عنوان IP')),
                ('user_agent', models.TextField(blank=True, verbose_name='معلومات المتصفح')),
                ('downloaded_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ التحميل')),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='المستخدم')),
                ('wallpaper', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='wallpapers.wallpaper', verbose_name='الخلفية')),
            ],
            options={
                'verbose_name': 'تحميل',
                'verbose_name_plural': 'التحميلات',
                'ordering': ['-downloaded_at'],
            },
        ),
        migrations.CreateModel(
            name='Favorite',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإضافة')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='المستخدم')),
                ('wallpaper', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='wallpapers.wallpaper', verbose_name='الخلفية')),
            ],
            options={
                'verbose_name': 'مفضلة',
                'verbose_name_plural': 'المفضلات',
                'ordering': ['-created_at'],
                'unique_together': {('user', 'wallpaper')},
            },
        ),
    ]
