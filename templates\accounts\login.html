{% extends 'base.html' %}
{% load crispy_forms_tags %}

{% block title %}تسجيل الدخول - موقع الخلفيات{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-md-6 col-lg-5">
            <div class="card shadow">
                <div class="card-body p-5">
                    <div class="text-center mb-4">
                        <i class="fas fa-sign-in-alt fa-3x text-primary mb-3"></i>
                        <h2 class="card-title">تسجيل الدخول</h2>
                        <p class="text-muted">أدخل بياناتك للوصول إلى حسابك</p>
                    </div>
                    
                    {% crispy form %}
                    
                    <div class="text-center mt-4">
                        <p class="mb-2">
                            <a href="{% url 'accounts:password_reset' %}" class="text-decoration-none">
                                نسيت كلمة المرور؟
                            </a>
                        </p>
                        <p class="mb-0">
                            ليس لديك حساب؟ 
                            <a href="{% url 'accounts:signup' %}" class="text-decoration-none fw-bold">
                                إنشاء حساب جديد
                            </a>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    .card {
        border-radius: 1rem;
        border: none;
    }
    
    .card-body {
        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        border-radius: 1rem;
    }
    
    .form-control:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    }
    
    .btn-primary {
        background: linear-gradient(45deg, #667eea, #764ba2);
        border: none;
        border-radius: 0.5rem;
        padding: 0.75rem 1.5rem;
        font-weight: 600;
        transition: all 0.3s ease;
    }
    
    .btn-primary:hover {
        background: linear-gradient(45deg, #5a6fd8, #6a4190);
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
    }
</style>
{% endblock %}
