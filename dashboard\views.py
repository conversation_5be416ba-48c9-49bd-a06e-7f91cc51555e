from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.admin.views.decorators import staff_member_required
from django.contrib.auth.decorators import user_passes_test
from django.contrib import messages
from django.db.models import Count, Sum, Q
from django.utils import timezone
from django.http import JsonResponse
from django.core.paginator import Paginator
from datetime import datetime, timedelta
from accounts.models import User
from wallpapers.models import Wallpaper, Category, Download
from notifications.models import Notification
from .models import SiteSettings, AdminLog, Statistics


def is_admin(user):
    """التحقق من أن المستخدم مدير"""
    return user.is_staff or user.is_superuser


@user_passes_test(is_admin)
def dashboard_home(request):
    """الصفحة الرئيسية للوحة التحكم"""
    # إحصائيات عامة
    total_users = User.objects.count()
    total_wallpapers = Wallpaper.objects.count()
    total_downloads = Download.objects.count()
    total_categories = Category.objects.count()

    # إحصائيات هذا الشهر
    current_month = timezone.now().replace(day=1)
    new_users_this_month = User.objects.filter(date_joined__gte=current_month).count()
    new_wallpapers_this_month = Wallpaper.objects.filter(created_at__gte=current_month).count()
    downloads_this_month = Download.objects.filter(downloaded_at__gte=current_month).count()

    # أحدث المستخدمين
    recent_users = User.objects.order_by('-date_joined')[:5]

    # أحدث الخلفيات
    recent_wallpapers = Wallpaper.objects.order_by('-created_at')[:5]

    # الخلفيات الأكثر تحميلاً
    popular_wallpapers = Wallpaper.objects.order_by('-download_count')[:5]

    # إحصائيات الأسبوع الماضي
    last_week = timezone.now() - timedelta(days=7)
    weekly_stats = []
    for i in range(7):
        date = last_week + timedelta(days=i)
        daily_downloads = Download.objects.filter(
            downloaded_at__date=date.date()
        ).count()
        weekly_stats.append({
            'date': date.strftime('%Y-%m-%d'),
            'downloads': daily_downloads
        })

    context = {
        'total_users': total_users,
        'total_wallpapers': total_wallpapers,
        'total_downloads': total_downloads,
        'total_categories': total_categories,
        'new_users_this_month': new_users_this_month,
        'new_wallpapers_this_month': new_wallpapers_this_month,
        'downloads_this_month': downloads_this_month,
        'recent_users': recent_users,
        'recent_wallpapers': recent_wallpapers,
        'popular_wallpapers': popular_wallpapers,
        'weekly_stats': weekly_stats,
    }

    return render(request, 'dashboard/home.html', context)


@user_passes_test(is_admin)
def wallpaper_management(request):
    """إدارة الخلفيات"""
    wallpapers_list = Wallpaper.objects.select_related('category', 'uploader').order_by('-created_at')

    # البحث والتصفية
    search_query = request.GET.get('q')
    if search_query:
        wallpapers_list = wallpapers_list.filter(
            Q(title__icontains=search_query) |
            Q(description__icontains=search_query) |
            Q(uploader__first_name__icontains=search_query) |
            Q(uploader__last_name__icontains=search_query)
        )

    category_filter = request.GET.get('category')
    if category_filter:
        wallpapers_list = wallpapers_list.filter(category_id=category_filter)

    status_filter = request.GET.get('status')
    if status_filter == 'active':
        wallpapers_list = wallpapers_list.filter(is_active=True)
    elif status_filter == 'inactive':
        wallpapers_list = wallpapers_list.filter(is_active=False)
    elif status_filter == 'featured':
        wallpapers_list = wallpapers_list.filter(is_featured=True)

    # ترقيم الصفحات
    paginator = Paginator(wallpapers_list, 20)
    page_number = request.GET.get('page')
    wallpapers = paginator.get_page(page_number)

    categories = Category.objects.all()

    context = {
        'wallpapers': wallpapers,
        'categories': categories,
        'search_query': search_query,
        'category_filter': category_filter,
        'status_filter': status_filter,
    }

    return render(request, 'dashboard/wallpaper_management.html', context)


@user_passes_test(is_admin)
def user_management(request):
    """إدارة المستخدمين"""
    users_list = User.objects.order_by('-date_joined')

    # البحث والتصفية
    search_query = request.GET.get('q')
    if search_query:
        users_list = users_list.filter(
            Q(first_name__icontains=search_query) |
            Q(last_name__icontains=search_query) |
            Q(email__icontains=search_query) |
            Q(username__icontains=search_query)
        )

    status_filter = request.GET.get('status')
    if status_filter == 'active':
        users_list = users_list.filter(is_active=True)
    elif status_filter == 'inactive':
        users_list = users_list.filter(is_active=False)
    elif status_filter == 'staff':
        users_list = users_list.filter(is_staff=True)
    elif status_filter == 'verified':
        users_list = users_list.filter(is_verified=True)

    # ترقيم الصفحات
    paginator = Paginator(users_list, 20)
    page_number = request.GET.get('page')
    users = paginator.get_page(page_number)

    context = {
        'users': users,
        'search_query': search_query,
        'status_filter': status_filter,
    }

    return render(request, 'dashboard/user_management.html', context)


@user_passes_test(is_admin)
def statistics_view(request):
    """عرض الإحصائيات"""
    # إحصائيات عامة
    stats = {
        'total_users': User.objects.count(),
        'active_users': User.objects.filter(is_active=True).count(),
        'verified_users': User.objects.filter(is_verified=True).count(),
        'total_wallpapers': Wallpaper.objects.count(),
        'active_wallpapers': Wallpaper.objects.filter(is_active=True).count(),
        'featured_wallpapers': Wallpaper.objects.filter(is_featured=True).count(),
        'total_downloads': Download.objects.count(),
        'total_categories': Category.objects.count(),
    }

    # إحصائيات شهرية (آخر 12 شهر)
    monthly_stats = []
    for i in range(12):
        date = timezone.now().replace(day=1) - timedelta(days=30*i)
        month_start = date.replace(day=1)
        if i == 0:
            month_end = timezone.now()
        else:
            month_end = (month_start + timedelta(days=32)).replace(day=1) - timedelta(days=1)

        new_users = User.objects.filter(
            date_joined__gte=month_start,
            date_joined__lte=month_end
        ).count()

        new_wallpapers = Wallpaper.objects.filter(
            created_at__gte=month_start,
            created_at__lte=month_end
        ).count()

        downloads = Download.objects.filter(
            downloaded_at__gte=month_start,
            downloaded_at__lte=month_end
        ).count()

        monthly_stats.append({
            'month': month_start.strftime('%Y-%m'),
            'month_name': month_start.strftime('%B %Y'),
            'new_users': new_users,
            'new_wallpapers': new_wallpapers,
            'downloads': downloads,
        })

    monthly_stats.reverse()

    # إحصائيات الفئات
    category_stats = Category.objects.annotate(
        wallpaper_count=Count('wallpaper'),
        download_count=Sum('wallpaper__download_count')
    ).order_by('-wallpaper_count')

    # أكثر المستخدمين نشاطاً
    top_uploaders = User.objects.annotate(
        upload_count=Count('wallpaper')
    ).filter(upload_count__gt=0).order_by('-upload_count')[:10]

    context = {
        'stats': stats,
        'monthly_stats': monthly_stats,
        'category_stats': category_stats,
        'top_uploaders': top_uploaders,
    }

    return render(request, 'dashboard/statistics.html', context)


@user_passes_test(is_admin)
def site_settings_view(request):
    """إعدادات الموقع"""
    settings, created = SiteSettings.objects.get_or_create(id=1)

    if request.method == 'POST':
        # تحديث الإعدادات
        settings.site_name = request.POST.get('site_name', settings.site_name)
        settings.site_description = request.POST.get('site_description', settings.site_description)
        settings.contact_email = request.POST.get('contact_email', settings.contact_email)
        settings.facebook_url = request.POST.get('facebook_url', settings.facebook_url)
        settings.twitter_url = request.POST.get('twitter_url', settings.twitter_url)
        settings.instagram_url = request.POST.get('instagram_url', settings.instagram_url)
        settings.youtube_url = request.POST.get('youtube_url', settings.youtube_url)
        settings.max_upload_size = int(request.POST.get('max_upload_size', settings.max_upload_size))
        settings.wallpapers_per_page = int(request.POST.get('wallpapers_per_page', settings.wallpapers_per_page))
        settings.enable_user_uploads = request.POST.get('enable_user_uploads') == 'on'
        settings.enable_comments = request.POST.get('enable_comments') == 'on'
        settings.maintenance_mode = request.POST.get('maintenance_mode') == 'on'
        settings.maintenance_message = request.POST.get('maintenance_message', settings.maintenance_message)

        # رفع الشعار
        if 'site_logo' in request.FILES:
            settings.site_logo = request.FILES['site_logo']

        settings.save()

        # تسجيل النشاط
        AdminLog.objects.create(
            admin_user=request.user,
            action='update',
            target_model='SiteSettings',
            target_id=settings.id,
            description='تحديث إعدادات الموقع',
            ip_address=request.META.get('REMOTE_ADDR', ''),
            user_agent=request.META.get('HTTP_USER_AGENT', '')
        )

        messages.success(request, 'تم تحديث إعدادات الموقع بنجاح!')
        return redirect('dashboard:settings')

    context = {
        'settings': settings,
    }

    return render(request, 'dashboard/settings.html', context)


@user_passes_test(is_admin)
def toggle_wallpaper_status(request, wallpaper_id):
    """تبديل حالة الخلفية (نشط/غير نشط)"""
    if request.method == 'POST':
        wallpaper = get_object_or_404(Wallpaper, id=wallpaper_id)
        wallpaper.is_active = not wallpaper.is_active
        wallpaper.save()

        # تسجيل النشاط
        AdminLog.objects.create(
            admin_user=request.user,
            action='update',
            target_model='Wallpaper',
            target_id=wallpaper.id,
            description=f'تغيير حالة الخلفية "{wallpaper.title}" إلى {"نشط" if wallpaper.is_active else "غير نشط"}',
            ip_address=request.META.get('REMOTE_ADDR', ''),
            user_agent=request.META.get('HTTP_USER_AGENT', '')
        )

        return JsonResponse({
            'success': True,
            'is_active': wallpaper.is_active,
            'message': f'تم تغيير حالة الخلفية إلى {"نشط" if wallpaper.is_active else "غير نشط"}'
        })

    return JsonResponse({'success': False})


@user_passes_test(is_admin)
def toggle_wallpaper_featured(request, wallpaper_id):
    """تبديل حالة الخلفية المميزة"""
    if request.method == 'POST':
        wallpaper = get_object_or_404(Wallpaper, id=wallpaper_id)
        wallpaper.is_featured = not wallpaper.is_featured
        wallpaper.save()

        # تسجيل النشاط
        AdminLog.objects.create(
            admin_user=request.user,
            action='update',
            target_model='Wallpaper',
            target_id=wallpaper.id,
            description=f'تغيير حالة الخلفية "{wallpaper.title}" إلى {"مميز" if wallpaper.is_featured else "عادي"}',
            ip_address=request.META.get('REMOTE_ADDR', ''),
            user_agent=request.META.get('HTTP_USER_AGENT', '')
        )

        return JsonResponse({
            'success': True,
            'is_featured': wallpaper.is_featured,
            'message': f'تم تغيير حالة الخلفية إلى {"مميز" if wallpaper.is_featured else "عادي"}'
        })

    return JsonResponse({'success': False})
