from django.shortcuts import render, get_object_or_404, redirect
from django.core.paginator import Paginator
from django.db.models import Q, Count
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.http import JsonResponse, HttpResponse
from django.views.decorators.http import require_POST
from django.views.generic import ListView, DetailView
from .models import Wallpaper, Category, Download, Favorite
from .forms import WallpaperSearchForm, WallpaperUploadForm


def home_view(request):
    """الصفحة الرئيسية"""
    # الخلفيات المميزة
    featured_wallpapers = Wallpaper.objects.filter(is_featured=True, is_active=True)[:8]

    # أحدث الخلفيات
    latest_wallpapers = Wallpaper.objects.filter(is_active=True).order_by('-created_at')[:12]

    # الأكثر تحميلاً
    popular_wallpapers = Wallpaper.objects.filter(is_active=True).order_by('-download_count')[:8]

    # الفئات
    categories = Category.objects.filter(is_active=True).annotate(
        wallpaper_count=Count('wallpaper')
    )

    # نموذج البحث
    search_form = WallpaperSearchForm()

    context = {
        'featured_wallpapers': featured_wallpapers,
        'latest_wallpapers': latest_wallpapers,
        'popular_wallpapers': popular_wallpapers,
        'categories': categories,
        'search_form': search_form,
    }

    return render(request, 'wallpapers/home.html', context)


class WallpaperListView(ListView):
    """عرض قائمة الخلفيات"""
    model = Wallpaper
    template_name = 'wallpapers/wallpaper_list.html'
    context_object_name = 'wallpapers'
    paginate_by = 20

    def get_queryset(self):
        queryset = Wallpaper.objects.filter(is_active=True)

        # تصفية حسب الفئة
        category_slug = self.kwargs.get('category_slug')
        if category_slug:
            queryset = queryset.filter(category__slug=category_slug)

        # تصفية حسب نوع الجهاز
        device_type = self.request.GET.get('device_type')
        if device_type:
            queryset = queryset.filter(device_type=device_type)

        # البحث
        search_query = self.request.GET.get('q')
        if search_query:
            queryset = queryset.filter(
                Q(title__icontains=search_query) |
                Q(description__icontains=search_query) |
                Q(tags__icontains=search_query)
            )

        # الترتيب
        sort_by = self.request.GET.get('sort', '-created_at')
        if sort_by in ['-created_at', '-download_count', '-view_count', 'title']:
            queryset = queryset.order_by(sort_by)

        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['categories'] = Category.objects.filter(is_active=True)
        context['search_form'] = WallpaperSearchForm(self.request.GET)

        # معلومات الفئة الحالية
        category_slug = self.kwargs.get('category_slug')
        if category_slug:
            context['current_category'] = get_object_or_404(Category, slug=category_slug)

        return context


class WallpaperDetailView(DetailView):
    """عرض تفاصيل الخلفية"""
    model = Wallpaper
    template_name = 'wallpapers/wallpaper_detail.html'
    context_object_name = 'wallpaper'
    slug_field = 'slug'
    slug_url_kwarg = 'slug'

    def get_object(self):
        obj = super().get_object()
        # زيادة عدد المشاهدات
        obj.view_count += 1
        obj.save(update_fields=['view_count'])
        return obj

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        wallpaper = self.object

        # خلفيات مشابهة
        context['related_wallpapers'] = Wallpaper.objects.filter(
            category=wallpaper.category,
            is_active=True
        ).exclude(id=wallpaper.id)[:6]

        # التحقق من المفضلة
        if self.request.user.is_authenticated:
            context['is_favorite'] = Favorite.objects.filter(
                user=self.request.user,
                wallpaper=wallpaper
            ).exists()

        return context


@require_POST
def download_wallpaper(request, slug):
    """تحميل الخلفية"""
    wallpaper = get_object_or_404(Wallpaper, slug=slug, is_active=True)

    # تسجيل التحميل
    Download.objects.create(
        wallpaper=wallpaper,
        user=request.user if request.user.is_authenticated else None,
        ip_address=request.META.get('REMOTE_ADDR', ''),
        user_agent=request.META.get('HTTP_USER_AGENT', '')
    )

    # زيادة عدد التحميلات
    wallpaper.download_count += 1
    wallpaper.save(update_fields=['download_count'])

    # إرجاع رابط التحميل
    return JsonResponse({
        'success': True,
        'download_url': wallpaper.image.url,
        'filename': wallpaper.image.name.split('/')[-1]
    })


@login_required
@require_POST
def toggle_favorite(request, slug):
    """إضافة/إزالة من المفضلة"""
    wallpaper = get_object_or_404(Wallpaper, slug=slug, is_active=True)

    favorite, created = Favorite.objects.get_or_create(
        user=request.user,
        wallpaper=wallpaper
    )

    if not created:
        favorite.delete()
        is_favorite = False
        message = 'تم إزالة الخلفية من المفضلة'
    else:
        is_favorite = True
        message = 'تم إضافة الخلفية إلى المفضلة'

    return JsonResponse({
        'success': True,
        'is_favorite': is_favorite,
        'message': message
    })
