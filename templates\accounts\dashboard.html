{% extends 'base.html' %}

{% block title %}لوحة التحكم - موقع الخلفيات{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row mb-4">
        <div class="col-12">
            <h2 class="mb-0">مرحباً، {{ user.get_full_name }}</h2>
            <p class="text-muted">إليك نظرة عامة على نشاطك</p>
        </div>
    </div>
    
    <!-- إحصائيات سريعة -->
    <div class="row mb-5">
        <div class="col-md-3 mb-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0">{{ user_wallpapers_count }}</h4>
                            <p class="mb-0">خلفياتي</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-image fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3 mb-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0">{{ user_favorites_count }}</h4>
                            <p class="mb-0">المفضلة</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-heart fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3 mb-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0">{{ user_downloads_count }}</h4>
                            <p class="mb-0">التحميلات</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-download fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3 mb-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0">0</h4>
                            <p class="mb-0">الإشعارات</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-bell fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row">
        <!-- الخلفيات الأخيرة -->
        <div class="col-md-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">خلفياتي الأخيرة</h5>
                </div>
                <div class="card-body">
                    {% if recent_wallpapers %}
                        {% for wallpaper in recent_wallpapers %}
                            <div class="d-flex align-items-center mb-3">
                                {% if wallpaper.thumbnail %}
                                    <img src="{{ wallpaper.thumbnail.url }}" alt="{{ wallpaper.title }}" class="rounded me-3" width="60" height="45" style="object-fit: cover;">
                                {% else %}
                                    <div class="bg-light rounded me-3 d-flex align-items-center justify-content-center" style="width: 60px; height: 45px;">
                                        <i class="fas fa-image text-muted"></i>
                                    </div>
                                {% endif %}
                                <div class="flex-grow-1">
                                    <h6 class="mb-1">{{ wallpaper.title }}</h6>
                                    <small class="text-muted">{{ wallpaper.download_count }} تحميل</small>
                                </div>
                                <small class="text-muted">{{ wallpaper.created_at|date:"d/m" }}</small>
                            </div>
                        {% endfor %}
                        <a href="#" class="btn btn-outline-primary btn-sm">عرض الكل</a>
                    {% else %}
                        <p class="text-muted text-center py-3">لم تقم برفع أي خلفيات بعد</p>
                        <div class="text-center">
                            <a href="#" class="btn btn-primary">رفع خلفية جديدة</a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
        
        <!-- المفضلة الأخيرة -->
        <div class="col-md-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">المفضلة الأخيرة</h5>
                </div>
                <div class="card-body">
                    {% if recent_favorites %}
                        {% for favorite in recent_favorites %}
                            <div class="d-flex align-items-center mb-3">
                                {% if favorite.wallpaper.thumbnail %}
                                    <img src="{{ favorite.wallpaper.thumbnail.url }}" alt="{{ favorite.wallpaper.title }}" class="rounded me-3" width="60" height="45" style="object-fit: cover;">
                                {% else %}
                                    <div class="bg-light rounded me-3 d-flex align-items-center justify-content-center" style="width: 60px; height: 45px;">
                                        <i class="fas fa-image text-muted"></i>
                                    </div>
                                {% endif %}
                                <div class="flex-grow-1">
                                    <h6 class="mb-1">{{ favorite.wallpaper.title }}</h6>
                                    <small class="text-muted">{{ favorite.wallpaper.category.name }}</small>
                                </div>
                                <small class="text-muted">{{ favorite.created_at|date:"d/m" }}</small>
                            </div>
                        {% endfor %}
                        <a href="#" class="btn btn-outline-primary btn-sm">عرض الكل</a>
                    {% else %}
                        <p class="text-muted text-center py-3">لا توجد خلفيات مفضلة</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    
    <!-- روابط سريعة -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">روابط سريعة</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 mb-2">
                            <a href="{% url 'accounts:profile' %}" class="btn btn-outline-primary w-100">
                                <i class="fas fa-user me-2"></i>الملف الشخصي
                            </a>
                        </div>
                        <div class="col-md-3 mb-2">
                            <a href="#" class="btn btn-outline-success w-100">
                                <i class="fas fa-upload me-2"></i>رفع خلفية
                            </a>
                        </div>
                        <div class="col-md-3 mb-2">
                            <a href="#" class="btn btn-outline-info w-100">
                                <i class="fas fa-heart me-2"></i>المفضلة
                            </a>
                        </div>
                        <div class="col-md-3 mb-2">
                            <a href="#" class="btn btn-outline-warning w-100">
                                <i class="fas fa-bell me-2"></i>الإشعارات
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    .card {
        border: none;
        border-radius: 1rem;
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        transition: all 0.3s ease;
    }
    
    .card:hover {
        transform: translateY(-2px);
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    }
    
    .card-header {
        background: linear-gradient(45deg, #667eea, #764ba2);
        color: white;
        border-radius: 1rem 1rem 0 0 !important;
        border: none;
    }
    
    .bg-primary {
        background: linear-gradient(45deg, #667eea, #764ba2) !important;
    }
    
    .bg-success {
        background: linear-gradient(45deg, #56ab2f, #a8e6cf) !important;
    }
    
    .bg-info {
        background: linear-gradient(45deg, #3498db, #85c1e9) !important;
    }
    
    .bg-warning {
        background: linear-gradient(45deg, #f39c12, #f7dc6f) !important;
    }
</style>
{% endblock %}
