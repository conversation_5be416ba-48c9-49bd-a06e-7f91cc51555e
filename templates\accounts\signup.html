{% extends 'base.html' %}
{% load crispy_forms_tags %}

{% block title %}إنشاء حساب جديد - موقع الخلفيات{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-md-8 col-lg-6">
            <div class="card shadow">
                <div class="card-body p-5">
                    <div class="text-center mb-4">
                        <i class="fas fa-user-plus fa-3x text-primary mb-3"></i>
                        <h2 class="card-title">إنشاء حساب جديد</h2>
                        <p class="text-muted">انضم إلينا واستمتع بأفضل الخلفيات</p>
                    </div>
                    
                    {% crispy form %}
                    
                    <div class="text-center mt-4">
                        <p class="mb-0">
                            لديك حساب بالفعل؟ 
                            <a href="{% url 'accounts:login' %}" class="text-decoration-none fw-bold">
                                تسجيل الدخول
                            </a>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    .card {
        border-radius: 1rem;
        border: none;
    }
    
    .card-body {
        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        border-radius: 1rem;
    }
    
    .form-control:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    }
    
    .btn-primary {
        background: linear-gradient(45deg, #667eea, #764ba2);
        border: none;
        border-radius: 0.5rem;
        padding: 0.75rem 1.5rem;
        font-weight: 600;
        transition: all 0.3s ease;
    }
    
    .btn-primary:hover {
        background: linear-gradient(45deg, #5a6fd8, #6a4190);
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
    }
    
    .form-row .col-md-6 {
        padding-left: 0.5rem;
        padding-right: 0.5rem;
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
    // تحقق من قوة كلمة المرور
    document.addEventListener('DOMContentLoaded', function() {
        const password1 = document.getElementById('id_password1');
        const password2 = document.getElementById('id_password2');
        
        if (password1 && password2) {
            password2.addEventListener('input', function() {
                if (password1.value !== password2.value) {
                    password2.setCustomValidity('كلمات المرور غير متطابقة');
                } else {
                    password2.setCustomValidity('');
                }
            });
        }
    });
</script>
{% endblock %}
