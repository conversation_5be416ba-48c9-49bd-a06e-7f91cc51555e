# Generated by Django 5.2.3 on 2025-09-04 19:57

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='SiteSettings',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('site_name', models.CharField(default='موقع الخلفيات', max_length=100, verbose_name='اسم الموقع')),
                ('site_description', models.TextField(default='موقع لتحميل خلفيات عالية الجودة', verbose_name='وصف الموقع')),
                ('site_logo', models.ImageField(blank=True, upload_to='site/', verbose_name='شعار الموقع')),
                ('contact_email', models.EmailField(default='<EMAIL>', max_length=254, verbose_name='بريد التواصل')),
                ('facebook_url', models.URLField(blank=True, verbose_name='رابط فيسبوك')),
                ('twitter_url', models.URLField(blank=True, verbose_name='رابط تويتر')),
                ('instagram_url', models.URLField(blank=True, verbose_name='رابط إنستغرام')),
                ('youtube_url', models.URLField(blank=True, verbose_name='رابط يوتيوب')),
                ('max_upload_size', models.PositiveIntegerField(default=10, verbose_name='الحد الأقصى لحجم الرفع (MB)')),
                ('wallpapers_per_page', models.PositiveIntegerField(default=20, verbose_name='عدد الخلفيات في الصفحة')),
                ('enable_user_uploads', models.BooleanField(default=True, verbose_name='السماح برفع المستخدمين')),
                ('enable_comments', models.BooleanField(default=True, verbose_name='السماح بالتعليقات')),
                ('maintenance_mode', models.BooleanField(default=False, verbose_name='وضع الصيانة')),
                ('maintenance_message', models.TextField(blank=True, verbose_name='رسالة الصيانة')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'إعدادات الموقع',
                'verbose_name_plural': 'إعدادات الموقع',
            },
        ),
        migrations.CreateModel(
            name='Statistics',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateField(unique=True, verbose_name='التاريخ')),
                ('total_users', models.PositiveIntegerField(default=0, verbose_name='إجمالي المستخدمين')),
                ('new_users', models.PositiveIntegerField(default=0, verbose_name='المستخدمون الجدد')),
                ('total_wallpapers', models.PositiveIntegerField(default=0, verbose_name='إجمالي الخلفيات')),
                ('new_wallpapers', models.PositiveIntegerField(default=0, verbose_name='الخلفيات الجديدة')),
                ('total_downloads', models.PositiveIntegerField(default=0, verbose_name='إجمالي التحميلات')),
                ('daily_downloads', models.PositiveIntegerField(default=0, verbose_name='التحميلات اليومية')),
                ('page_views', models.PositiveIntegerField(default=0, verbose_name='مشاهدات الصفحة')),
                ('unique_visitors', models.PositiveIntegerField(default=0, verbose_name='الزوار الفريدون')),
            ],
            options={
                'verbose_name': 'إحصائية',
                'verbose_name_plural': 'الإحصائيات',
                'ordering': ['-date'],
            },
        ),
        migrations.CreateModel(
            name='AdminLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('action', models.CharField(choices=[('create', 'إنشاء'), ('update', 'تحديث'), ('delete', 'حذف'), ('login', 'تسجيل دخول'), ('logout', 'تسجيل خروج'), ('upload', 'رفع'), ('download', 'تحميل')], max_length=20, verbose_name='الإجراء')),
                ('target_model', models.CharField(max_length=100, verbose_name='النموذج المستهدف')),
                ('target_id', models.PositiveIntegerField(blank=True, null=True, verbose_name='معرف الهدف')),
                ('description', models.TextField(verbose_name='الوصف')),
                ('ip_address', models.GenericIPAddressField(verbose_name='عنوان IP')),
                ('user_agent', models.TextField(blank=True, verbose_name='معلومات المتصفح')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('admin_user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='المدير')),
            ],
            options={
                'verbose_name': 'سجل إداري',
                'verbose_name_plural': 'السجلات الإدارية',
                'ordering': ['-created_at'],
            },
        ),
    ]
