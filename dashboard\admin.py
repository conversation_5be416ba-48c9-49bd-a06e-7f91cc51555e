from django.contrib import admin
from .models import SiteSettings, AdminLog, Statistics


@admin.register(SiteSettings)
class SiteSettingsAdmin(admin.ModelAdmin):
    list_display = ('site_name', 'contact_email', 'maintenance_mode', 'updated_at')

    fieldsets = (
        ('معلومات الموقع', {
            'fields': ('site_name', 'site_description', 'site_logo', 'contact_email')
        }),
        ('روابط التواصل الاجتماعي', {
            'fields': ('facebook_url', 'twitter_url', 'instagram_url', 'youtube_url')
        }),
        ('إعدادات الرفع والعرض', {
            'fields': ('max_upload_size', 'wallpapers_per_page', 'enable_user_uploads', 'enable_comments')
        }),
        ('وضع الصيانة', {
            'fields': ('maintenance_mode', 'maintenance_message')
        }),
    )

    def has_add_permission(self, request):
        # السماح بإضافة سجل واحد فقط
        return not SiteSettings.objects.exists()

    def has_delete_permission(self, request, obj=None):
        # منع حذف إعدادات الموقع
        return False


@admin.register(AdminLog)
class AdminLogAdmin(admin.ModelAdmin):
    list_display = ('admin_user', 'action', 'target_model', 'description', 'ip_address', 'created_at')
    list_filter = ('action', 'target_model', 'created_at')
    search_fields = ('admin_user__email', 'description', 'ip_address')
    readonly_fields = ('admin_user', 'action', 'target_model', 'target_id', 'description', 'ip_address', 'user_agent', 'created_at')

    def has_add_permission(self, request):
        return False

    def has_change_permission(self, request, obj=None):
        return False


@admin.register(Statistics)
class StatisticsAdmin(admin.ModelAdmin):
    list_display = ('date', 'total_users', 'new_users', 'total_wallpapers', 'new_wallpapers', 'daily_downloads')
    list_filter = ('date',)
    readonly_fields = ('date', 'total_users', 'new_users', 'total_wallpapers', 'new_wallpapers', 'total_downloads', 'daily_downloads', 'page_views', 'unique_visitors')

    def has_add_permission(self, request):
        return False

    def has_change_permission(self, request, obj=None):
        return False
