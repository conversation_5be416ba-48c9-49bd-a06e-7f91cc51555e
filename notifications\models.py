from django.db import models
from django.contrib.auth import get_user_model

User = get_user_model()


class Notification(models.Model):
    """نموذج الإشعارات"""
    TYPE_CHOICES = [
        ('new_wallpaper', 'خلفية جديدة'),
        ('admin_message', 'رسالة إدارية'),
        ('system', 'إشعار نظام'),
        ('welcome', 'ترحيب'),
    ]

    recipient = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name="المستقبل")
    title = models.CharField(max_length=200, verbose_name="العنوان")
    message = models.TextField(verbose_name="الرسالة")
    notification_type = models.CharField(max_length=20, choices=TYPE_CHOICES, verbose_name="نوع الإشعار")
    is_read = models.BooleanField(default=False, verbose_name="مقروء")
    url = models.URLField(blank=True, verbose_name="الرابط")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    read_at = models.DateTimeField(null=True, blank=True, verbose_name="تاريخ القراءة")

    class Meta:
        verbose_name = "إشعار"
        verbose_name_plural = "الإشعارات"
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.title} - {self.recipient.get_full_name()}"

    def mark_as_read(self):
        """تحديد الإشعار كمقروء"""
        from django.utils import timezone
        self.is_read = True
        self.read_at = timezone.now()
        self.save()


class EmailNotification(models.Model):
    """نموذج إشعارات البريد الإلكتروني"""
    recipient_email = models.EmailField(verbose_name="البريد الإلكتروني")
    subject = models.CharField(max_length=200, verbose_name="الموضوع")
    message = models.TextField(verbose_name="الرسالة")
    is_sent = models.BooleanField(default=False, verbose_name="تم الإرسال")
    sent_at = models.DateTimeField(null=True, blank=True, verbose_name="تاريخ الإرسال")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    error_message = models.TextField(blank=True, verbose_name="رسالة الخطأ")

    class Meta:
        verbose_name = "إشعار بريد إلكتروني"
        verbose_name_plural = "إشعارات البريد الإلكتروني"
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.subject} - {self.recipient_email}"
