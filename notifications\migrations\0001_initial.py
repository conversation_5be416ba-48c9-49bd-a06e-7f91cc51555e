# Generated by Django 5.2.3 on 2025-09-04 19:57

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='EmailNotification',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('recipient_email', models.EmailField(max_length=254, verbose_name='البريد الإلكتروني')),
                ('subject', models.CharField(max_length=200, verbose_name='الموضوع')),
                ('message', models.TextField(verbose_name='الرسالة')),
                ('is_sent', models.BooleanField(default=False, verbose_name='تم الإرسال')),
                ('sent_at', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الإرسال')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('error_message', models.TextField(blank=True, verbose_name='رسالة الخطأ')),
            ],
            options={
                'verbose_name': 'إشعار بريد إلكتروني',
                'verbose_name_plural': 'إشعارات البريد الإلكتروني',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='Notification',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200, verbose_name='العنوان')),
                ('message', models.TextField(verbose_name='الرسالة')),
                ('notification_type', models.CharField(choices=[('new_wallpaper', 'خلفية جديدة'), ('admin_message', 'رسالة إدارية'), ('system', 'إشعار نظام'), ('welcome', 'ترحيب')], max_length=20, verbose_name='نوع الإشعار')),
                ('is_read', models.BooleanField(default=False, verbose_name='مقروء')),
                ('url', models.URLField(blank=True, verbose_name='الرابط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('read_at', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ القراءة')),
                ('recipient', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='المستقبل')),
            ],
            options={
                'verbose_name': 'إشعار',
                'verbose_name_plural': 'الإشعارات',
                'ordering': ['-created_at'],
            },
        ),
    ]
