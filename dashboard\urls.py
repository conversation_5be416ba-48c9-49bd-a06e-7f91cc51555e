from django.urls import path
from . import views

app_name = 'dashboard'

urlpatterns = [
    # لوحة التحكم الرئيسية
    path('', views.dashboard_home, name='home'),

    # إدارة الخلفيات
    path('wallpapers/', views.wallpaper_management, name='wallpapers'),
    path('wallpapers/<int:wallpaper_id>/toggle-status/', views.toggle_wallpaper_status, name='toggle_wallpaper_status'),
    path('wallpapers/<int:wallpaper_id>/toggle-featured/', views.toggle_wallpaper_featured, name='toggle_wallpaper_featured'),

    # إدارة المستخدمين
    path('users/', views.user_management, name='users'),

    # الإحصائيات
    path('statistics/', views.statistics_view, name='statistics'),

    # الإعدادات
    path('settings/', views.site_settings_view, name='settings'),
]
