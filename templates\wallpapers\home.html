{% extends 'base.html' %}
{% load crispy_forms_tags %}

{% block title %}موقع الخلفيات - أفضل خلفيات عالية الجودة{% endblock %}

{% block content %}
<!-- Hero Section -->
<section class="hero-section">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6">
                <h1 class="display-4 fw-bold mb-4">
                    اكتشف أجمل الخلفيات
                </h1>
                <p class="lead mb-4">
                    مجموعة ضخمة من الخلفيات عالية الجودة للكمبيوتر والهواتف الذكية
                </p>
                <div class="d-flex gap-3">
                    <a href="{% url 'wallpapers:list' %}" class="btn btn-light btn-lg">
                        <i class="fas fa-images me-2"></i>
                        تصفح الخلفيات
                    </a>
                    {% if user.is_authenticated %}
                        <a href="#" class="btn btn-outline-light btn-lg">
                            <i class="fas fa-upload me-2"></i>
                            رفع خلفية
                        </a>
                    {% endif %}
                </div>
            </div>
            <div class="col-lg-6">
                <div class="text-center">
                    <i class="fas fa-image fa-10x opacity-75"></i>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Search Section -->
<section class="py-5 bg-light">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="card shadow-sm">
                    <div class="card-body">
                        <h4 class="text-center mb-4">ابحث عن الخلفية المثالية</h4>
                        {% crispy search_form %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Categories Section -->
<section class="py-5">
    <div class="container">
        <h2 class="text-center mb-5">تصفح حسب الفئة</h2>
        <div class="row">
            {% for category in categories %}
                <div class="col-md-3 mb-4">
                    <a href="{% url 'wallpapers:category' category.slug %}" class="text-decoration-none">
                        <div class="card h-100 text-center category-card">
                            <div class="card-body">
                                {% if category.icon %}
                                    <i class="{{ category.icon }} fa-3x text-primary mb-3"></i>
                                {% else %}
                                    <i class="fas fa-folder fa-3x text-primary mb-3"></i>
                                {% endif %}
                                <h5 class="card-title">{{ category.name }}</h5>
                                <p class="text-muted">{{ category.wallpaper_count }} خلفية</p>
                            </div>
                        </div>
                    </a>
                </div>
            {% endfor %}
        </div>
    </div>
</section>

<!-- Featured Wallpapers -->
{% if featured_wallpapers %}
<section class="py-5 bg-light">
    <div class="container">
        <h2 class="text-center mb-5">الخلفيات المميزة</h2>
        <div class="masonry-grid">
            {% for wallpaper in featured_wallpapers %}
                <div class="masonry-item">
                    <div class="card wallpaper-card">
                        <div class="position-relative">
                            {% if wallpaper.thumbnail %}
                                <img src="{{ wallpaper.thumbnail.url }}" class="card-img-top" alt="{{ wallpaper.title }}">
                            {% else %}
                                <img src="{{ wallpaper.image.url }}" class="card-img-top" alt="{{ wallpaper.title }}" style="max-height: 300px; object-fit: cover;">
                            {% endif %}
                            <div class="position-absolute top-0 end-0 m-2">
                                <span class="badge bg-warning">
                                    <i class="fas fa-star me-1"></i>مميز
                                </span>
                            </div>
                        </div>
                        <div class="card-body">
                            <h6 class="card-title">{{ wallpaper.title }}</h6>
                            <div class="d-flex justify-content-between align-items-center">
                                <small class="text-muted">
                                    <i class="fas fa-download me-1"></i>
                                    {{ wallpaper.download_count }}
                                </small>
                                <small class="text-muted">{{ wallpaper.resolution }}</small>
                            </div>
                            <div class="mt-2">
                                <a href="{% url 'wallpapers:detail' wallpaper.slug %}" class="btn btn-primary btn-sm w-100">
                                    عرض التفاصيل
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            {% endfor %}
        </div>
    </div>
</section>
{% endif %}

<!-- Latest Wallpapers -->
<section class="py-5">
    <div class="container">
        <div class="d-flex justify-content-between align-items-center mb-5">
            <h2>أحدث الخلفيات</h2>
            <a href="{% url 'wallpapers:list' %}" class="btn btn-outline-primary">
                عرض الكل
                <i class="fas fa-arrow-left ms-2"></i>
            </a>
        </div>
        <div class="row">
            {% for wallpaper in latest_wallpapers %}
                <div class="col-lg-3 col-md-4 col-sm-6 mb-4">
                    <div class="card wallpaper-card h-100">
                        <div class="position-relative">
                            {% if wallpaper.thumbnail %}
                                <img src="{{ wallpaper.thumbnail.url }}" class="card-img-top" alt="{{ wallpaper.title }}" style="height: 200px; object-fit: cover;">
                            {% else %}
                                <img src="{{ wallpaper.image.url }}" class="card-img-top" alt="{{ wallpaper.title }}" style="height: 200px; object-fit: cover;">
                            {% endif %}
                            <div class="position-absolute top-0 start-0 m-2">
                                <span class="badge bg-success">جديد</span>
                            </div>
                        </div>
                        <div class="card-body d-flex flex-column">
                            <h6 class="card-title">{{ wallpaper.title }}</h6>
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <small class="text-muted">{{ wallpaper.category.name }}</small>
                                <small class="text-muted">{{ wallpaper.device_type }}</small>
                            </div>
                            <div class="mt-auto">
                                <a href="{% url 'wallpapers:detail' wallpaper.slug %}" class="btn btn-primary btn-sm w-100">
                                    عرض التفاصيل
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            {% endfor %}
        </div>
    </div>
</section>

<!-- Popular Wallpapers -->
{% if popular_wallpapers %}
<section class="py-5 bg-light">
    <div class="container">
        <h2 class="text-center mb-5">الأكثر تحميلاً</h2>
        <div class="row">
            {% for wallpaper in popular_wallpapers %}
                <div class="col-lg-3 col-md-4 col-sm-6 mb-4">
                    <div class="card wallpaper-card h-100">
                        <div class="position-relative">
                            {% if wallpaper.thumbnail %}
                                <img src="{{ wallpaper.thumbnail.url }}" class="card-img-top" alt="{{ wallpaper.title }}" style="height: 200px; object-fit: cover;">
                            {% else %}
                                <img src="{{ wallpaper.image.url }}" class="card-img-top" alt="{{ wallpaper.title }}" style="height: 200px; object-fit: cover;">
                            {% endif %}
                            <div class="position-absolute top-0 end-0 m-2">
                                <span class="badge bg-danger">
                                    <i class="fas fa-fire me-1"></i>شائع
                                </span>
                            </div>
                        </div>
                        <div class="card-body d-flex flex-column">
                            <h6 class="card-title">{{ wallpaper.title }}</h6>
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <small class="text-muted">
                                    <i class="fas fa-download me-1"></i>
                                    {{ wallpaper.download_count }}
                                </small>
                                <small class="text-muted">{{ wallpaper.resolution }}</small>
                            </div>
                            <div class="mt-auto">
                                <a href="{% url 'wallpapers:detail' wallpaper.slug %}" class="btn btn-primary btn-sm w-100">
                                    عرض التفاصيل
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            {% endfor %}
        </div>
    </div>
</section>
{% endif %}

<!-- Statistics Section -->
<section class="py-5">
    <div class="container">
        <div class="row text-center">
            <div class="col-md-3 mb-4">
                <div class="card bg-primary text-white">
                    <div class="card-body">
                        <i class="fas fa-images fa-3x mb-3"></i>
                        <h3>1000+</h3>
                        <p>خلفية عالية الجودة</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-4">
                <div class="card bg-success text-white">
                    <div class="card-body">
                        <i class="fas fa-users fa-3x mb-3"></i>
                        <h3>5000+</h3>
                        <p>مستخدم نشط</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-4">
                <div class="card bg-info text-white">
                    <div class="card-body">
                        <i class="fas fa-download fa-3x mb-3"></i>
                        <h3>50000+</h3>
                        <p>تحميل</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-4">
                <div class="card bg-warning text-white">
                    <div class="card-body">
                        <i class="fas fa-star fa-3x mb-3"></i>
                        <h3>4.8</h3>
                        <p>تقييم المستخدمين</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
{% endblock %}

{% block extra_css %}
<style>
    .category-card {
        transition: all 0.3s ease;
        border: none;
    }
    
    .category-card:hover {
        transform: translateY(-10px);
        box-shadow: 0 1rem 2rem rgba(0, 0, 0, 0.15);
    }
    
    .search-form .form-row {
        align-items: end;
    }
</style>
{% endblock %}
